package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.DesktopInspectionDetailsMeDaoImpl;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.impl.motorengineer.OnSiteInspectionDetailsMeDaoImpl;
import com.misyn.mcms.claim.dao.impl.motorengineer.TireCondtionMeDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.DesktopInspectionDetailsMeDao;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dao.motorengineer.OnSiteInspectionDetailsMeDao;
import com.misyn.mcms.claim.dao.motorengineer.TireCondtionMeDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.AssessorAllocationService;
import com.misyn.mcms.claim.service.InspectionDetailsService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class AssessorAllocationServiceImpl extends AbstractBaseService<AssessorAllocationServiceImpl> implements AssessorAllocationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorAllocationServiceImpl.class);
    private final Lock LOCK = new ReentrantLock();
    private AssessorAllocationDao assessorAllocationDao = new AssessorAllocationDaoImpl();
    private AssessorTimeExceedNotificationDao assessorTimeExceedNotificationDao = new AssessorTimeExceedNotificationDaoImpl();
    private SequenceKeyTableDao sequenceKeyTableDao = new SequenceKeyTableDaoImpl();
    private SpecialRemarkDao specialRemarkDao = new SpecialRemarkDaoImpl();

    private CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private AssessorDao assessorDao = new AssessorDaoImpl();
    private InspectionDao inspectionDao = new InspectionDaoImpl();
    private InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private NotificationDao notificationDao = new NotificationDaoImpl();
    private ApplicationAlertDao applicationAlertDao = new ApplicationAlertDaoImpl();
    private RequestAriDao requestAriDao = new RequestAriDaoImpl();
    private UserDao userDao = new UserDaoImpl();

    private MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private OnSiteInspectionDetailsMeDao onSiteInspectionDetailsDao = new OnSiteInspectionDetailsMeDaoImpl();
    private TireCondtionMeDao tireCondtionMeDao = new TireCondtionMeDaoImpl();
    private InspectionDetailsService inspectionDetailsService;
    private DesktopInspectionDetailsMeDao desktopInspectionDetailsMeDao = new DesktopInspectionDetailsMeDaoImpl();
    private RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();

    public AssessorAllocationServiceImpl() {
    }

    public AssessorAllocationServiceImpl(InspectionDetailsService inspectionDetailsService) {
        this.inspectionDetailsService = inspectionDetailsService;
    }


    @Override
    public AssessorAllocationDto insert(AssessorAllocationDto assessorAllocationDto, UserDto user) throws Exception {
        AssessorAllocationDto assessorAllocation = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            assessorAllocationDto.setInputUserId(user.getUserId());
            assessorAllocationDto.setInputDatetime(Utility.sysDateTime());
//            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, assessorAllocationDto.getClaimsId());
//            assessorAllocationDto.setClaimsDto(claimsDto);
            if (null != assessorAllocationDto.getAssessorDto()) {
                String assessorMobileNo = assessorDao.getAssessorMobileNo(connection, assessorAllocationDto.getAssessorDto().getCode().trim());
                String name = assessorDao.getAssessorName(connection, assessorAllocationDto.getAssessorDto().getCode());
                assessorAllocationDto.getAssessorDto().setName(name);
                assessorAllocationDto.getAssessorDto().setAssessorContactNo(assessorMobileNo);
                assessorAllocationDto.getAssessorDto().setCode(assessorAllocationDto.getAssessorDto().getCode().trim());
            }

            if (null != assessorAllocationDto.getInspectionDto() && assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.CALL_ESTIMATE) {
                assessorAllocationDto.setAssignDatetime(Utility.sysDateTime());
            }

            boolean isHighPriority = false;

            if (null != assessorAllocationDto.getInspectionDto() && assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                if (assessorAllocationDto.getPriority().equals("HIGH")) {
                    isHighPriority = true;
                    saveClaimsLogs(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user, "Desktop Inspection Assigned with High Priority", "Desktop Inspection Assigned with High Priority by " + user.getUserId());
                    saveClaimProcessFlow(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), 0, "Desktop Inspection Assigned With High Priority", user.getUserId(), Utility.sysDateTime(), assessorAllocationDto.getRteCode());
                }
            } else if (null != assessorAllocationDto.getInspectionDto() && assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION_ONLINE) {
                if (assessorAllocationDto.getPriority().equals("HIGH")) {
                    isHighPriority = true;
                    saveClaimsLogs(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user, "Remote Inspection Assigned with High Priority", "Remote Inspection Assigned with High Priority by " + user.getUserId());
                    saveClaimProcessFlow(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), 0, "Remote Inspection Assigned With High Priority", user.getUserId(), Utility.sysDateTime(), assessorAllocationDto.getRteCode());
                }
            }

            assessorAllocationDto.setDuration(getDuration(assessorAllocationDto));
            if (!assessorAllocationDto.getResponseValue().isEmpty()) {
                assessorAllocationDto.setResponse(Integer.parseInt(assessorAllocationDto.getResponseValue()));

            }
            if (AppConstant.ACCEPTED == assessorAllocationDto.getResponse()) {
                assessorAllocationDto.setJobStatusId(AppConstant.PENDING);
            } else {
                assessorAllocationDto.setJobStatusId(assessorAllocationDto.getResponse());
            }
            updateMaxJobId(connection, assessorAllocationDto);

            boolean reasign = false;
            if (null != assessorAllocationDto.getPreviousRefId()
                    && assessorAllocationDto.getPreviousRefId() > 0) {
                reasign = true;
                int reasonId = 0;
                if (null != assessorAllocationDto.getReassigningReasonDto()) {
                    reasonId = assessorAllocationDto.getReassigningReasonDto().getId();
                }
                assessorAllocationDao.updateJobStatusByJobId(connection, ClaimStatus.REASSIGNED.getClaimStatus(), reasonId, assessorAllocationDto.getPreviousRefId());
                assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.REASSIGNED.getClaimStatus(), assessorAllocationDto.getPreviousRefId());
                if (AppConstant.DESKTOP_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId()) {
                    inspectionDetailsDao.updateRecordStatusByRefNo(connection, ClaimStatus.REASSIGNED.getClaimStatus(), assessorAllocationDto.getPreviousRefId());
                }
                assessorAllocationDto.setReassigningReasonDto(new ReassigningReasonDto());
            }

            if (!assessorAllocationDto.getRemark().isEmpty()) {
                SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
                specialRemarkDto.setClaimNo(assessorAllocationDto.getClaimsDto().getClaimNo());
                specialRemarkDto.setRemark(assessorAllocationDto.getRemark());

                this.saveRemark(specialRemarkDto, user);
            }
            assessorAllocationDto.setCustomerMobileNo(assessorAllocationDto.getClaimsDto().getIntimationNo());
            if (AppConstant.REJECTED == assessorAllocationDto.getResponse()) {
                assessorAllocationDto.setRecordStatus(ClaimStatus.REJECTED.getClaimStatus());
            } else {
                assessorAllocationDto.setRecordStatus(ClaimStatus.JOB_ASSIGNED.getClaimStatus());
            }

            if (AppConstant.CALL_ESTIMATE == assessorAllocationDto.getInspectionDto().getInspectionId()) {
                assessorAllocationDto.setRecordStatus(ClaimStatus.APPROVED.getClaimStatus());
            }

            if (AppConstant.YES.equalsIgnoreCase(assessorAllocationDto.getAssessmentType())) {
                assessorAllocationDto.setAssessorType(AppConstant.INHOUSE);
            } else {
                assessorAllocationDto.setAssessorType(userDao.getAssessorType(connection, assessorAllocationDto.getAssessorDto().getCode().trim()));
            }

            assessorAllocation = assessorAllocationDao.insertMaster(connection, assessorAllocationDto);
            RequestAriDto requestAri = requestAriDao.searchByClaimNo(connection, assessorAllocationDto.getClaimsDto().getClaimNo());
            if (reasign) {
                requestAri = requestAriDao.searchByCompletedClaimNo(connection, assessorAllocationDto.getClaimsDto().getClaimNo());
                this.sendReassignedSmsToCustomer(assessorAllocationDto, connection, user);
            }
            if ((AppConstant.ARI_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId()
                    || AppConstant.SALVAGE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId()) && null != requestAri) {
                RequestAriDto requestAriDto = new RequestAriDto();
                requestAriDto.setStatus("C");
                requestAriDto.setAssignedAssessor(assessorAllocationDto.getAssessorDto().getName());
                requestAriDto.setAssignedAssessorCode(assessorAllocationDto.getAssessorDto().getCode());
                requestAriDto.setId(requestAri.getId());
                requestAriDto.setRefId(assessorAllocation.getRefNo());
                requestAriDao.updateStatusByClaimNo(connection, requestAriDto);
            } else if ((AppConstant.ARI_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId()
                    || AppConstant.SALVAGE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId())) {
                throw new Exception("Pending ARI not found");
            }

            if (null != assessorAllocationDto.getClaimsDto()) {
                ClaimsDto claimsDto = assessorAllocationDto.getClaimsDto();
                if (claimsDto.getClaimStatus() == AppConstant.CALL_CENTER_DRAFT || claimsDto.getClaimStatus() == AppConstant.CALL_CENTER_DRAFTASSIGNED) {
                    claimsDto.setClaimStatus(AppConstant.CALL_CENTER_DRAFTASSIGNED);
                } else {
                    claimsDto.setClaimStatus(ClaimStatus.ASSIGNED.getClaimStatus());
                }
                callCenterDao.updateClaimStatus(connection, assessorAllocationDto.getClaimsDto());
            }

            claimDocumentDao.updateJobRefNoByClaimNo(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), assessorAllocation.getRefNo());

            this.setAllocationNotification(assessorAllocationDto, user, assessorAllocation, connection, isHighPriority);

            List<String> assignUserbyClaimNo = getAssignUserbyClaimNo(connection, assessorAllocationDto.getClaimsDto().getClaimNo());

            if (AppConstant.REJECTED != assessorAllocationDto.getResponse()) {
                for (String assignUser : assignUserbyClaimNo) {
                    int accessUserTypeByUserId = userDao.getAccessUserTypeByUserId(connection, assignUser);
                    if (accessUserTypeByUserId == AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM || accessUserTypeByUserId == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR) {
                        rtePendingClaimDetailDao.savePendingJobs(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), accessUserTypeByUserId, assignUser);
                        break;
                    }
                }
                this.sendOnsiteAssessorAssignedSmsToCustomer(assessorAllocationDto, assessorAllocation, connection, user);
                this.sendOnsiteAssessorNotAssignedSmsToCustomer(assessorAllocationDto, assessorAllocation, connection, user);
                this.sendOnsiteAssessorAssignedSmsToAgent(assessorAllocationDto, assessorAllocation, connection, user);
                this.sendOnsiteAssessorNotAssignedSmsToAgent(assessorAllocationDto, assessorAllocation, connection, user);
                this.sendAriSalvageAssessorAssignedSmsToCustomer(assessorAllocationDto, assessorAllocation, connection, user);
                this.sendAriSalvageAssessorAssignedSmsToAgent(assessorAllocationDto, assessorAllocation, connection, user);
                this.sendInspectionAssignedSmsToAssessor(connection, assessorAllocationDto);
//                if (!reasign) {
                this.sendGarageDRSupplementaryAssessorReassignedSmsToCustomer(assessorAllocationDto, assessorAllocation, connection, user);
                this.sendGarageDRSupplementaryAssessorReassignedSmsToAgent(assessorAllocationDto, assessorAllocation, connection, user);
//                }
            } else {
                sendNotificationForAssignUser(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user, AppConstant.ZERO_INT);
            }

            assessorTimeExceedNotificationDao.insertMaster(connection, setValues(assessorAllocationDto, user));
            commitTransaction(connection);

        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return assessorAllocation;

    }

    private AssessorTimeExceedNotificationDto setValues(AssessorAllocationDto assessorAllocationDto, UserDto user) {
        AssessorTimeExceedNotificationDto dto = new AssessorTimeExceedNotificationDto();
        dto.setAssignedDateTime(assessorAllocationDto.getAssignDatetime());
        dto.setDuration(assessorAllocationDto.getDuration());
        dto.setAssessorType(assessorAllocationDto.getAssessorType());
        dto.setStatus(AppConstant.STRING_PENDING);
        dto.setClaimNo(assessorAllocationDto.getClaimsDto().getClaimNo());
        dto.setCreatedDate(new Date(System.currentTimeMillis()));
        dto.setInspectionTime(assessorAllocationDto.getJobFinishedDatetime());
        dto.setInspectionAssessor(assessorAllocationDto.getAssessorDto().getCode());
        dto.setCreatedBy(assessorAllocationDto.getInputUserId());
        dto.setCallCenterUser(user.getUserId());
        dto.setColourCode(AppConstant.DEFAULT_NOTIFICATION_COLOR_CODE);
        dto.setInspectionRefNo(assessorAllocationDto.getJobId());
        dto.setInspectionType(assessorAllocationDto.getInspectionDto().getInspectionId());
        dto.setClaimNo(assessorAllocationDto.getClaimsDto().getClaimNo());

        return dto;
    }

    private void sendReassignedSmsToCustomer(AssessorAllocationDto assessorAllocationDto, Connection connection, UserDto user) throws Exception {
        if (AppConstant.ON_SITE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() || AppConstant.DR_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() ||
                AppConstant.GARAGE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() || AppConstant.SUPPLEMENTARY_INSPECTION_SMS == assessorAllocationDto.getInspectionDto().getInspectionId()) {
            String inspectionType = inspectionDetailsDao.getInspectionTypeById(connection, assessorAllocationDto.getInspectionDto().getInspectionId());
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(inspectionType);
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getName());
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getAssessorContactNo());
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getVehicleNo());
            smsParameterList.add(String.valueOf(assessorAllocationDto.getClaimsDto().getClaimNo()));
            sendSmsMessage(connection, 46, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getCustMobileNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        }
    }

    private void sendAriSalvageAssessorAssignedSmsToAgent(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION
                || assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getPolicyDto().getCustName());
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getVehicleNo());
            sendSmsMessage(connection, 29, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_AGENT);
        }
    }

    private void sendAriSalvageAssessorAssignedSmsToCustomer(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION
                || assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getName());
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getAssessorContactNo());
            sendSmsMessage(connection, 28, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getCustMobileNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        }
    }

    private void sendGarageDRSupplementaryAssessorReassignedSmsToAgent(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION ||
                assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION ||
                assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getPolicyDto().getCustName());
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getVehicleNo());
            sendSmsMessage(connection, 27, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_AGENT);
        }
    }

    private void sendGarageDRSupplementaryAssessorReassignedSmsToCustomer(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION ||
                assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION ||
                assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getName());
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getAssessorContactNo());
            sendSmsMessage(connection, 26, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getCustMobileNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        }
    }

    private void setAllocationNotification(AssessorAllocationDto assessorAllocationDto, UserDto user, AssessorAllocationDto assessorAllocation, Connection connection, boolean isHighPriority) throws Exception {
        String message = "You have been assigned ".concat(inspectionDao.searchMaster(connection, assessorAllocation.getInspectionDto().getInspectionId()).getInspectionValue()).concat(" the Job No:").concat(assessorAllocation.getJobId());
        if ( assessorAllocation.getInspectionDto().getInspectionId() != AppConstant.DESKTOP_INSPECTION &&
                assessorAllocation.getInspectionDto().getInspectionId() != AppConstant.CALL_ESTIMATE &&
                assessorAllocation.getInspectionDto().getInspectionId() != AppConstant.DESKTOP_INSPECTION_ONLINE ) {
            AssessorDto assessorUserName = assessorDao.getAssessorUserName(connection, assessorAllocation.getAssessorDto().getCode());
            if (null != assessorUserName) {
                saveNotification(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user.getUserId(), assessorUserName.getUserName(), message,
                        AppConstant.ASSESSOR_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(assessorAllocation.getClaimsDto().getClaimNo())).concat("&P_N_REF_NO=").concat(Integer.toString(assessorAllocation.getRefNo())), assessorAllocation.getRefNo());
            }
        } else if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
            String messages = isHighPriority ? "You have been assigned High Prioritized Desktop Assessment the Job No:" : "You have been assigned Desktop Assessment the Job No:";
            String finalizedMessage = messages.concat(assessorAllocation.getJobId());

            String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(assessorAllocation.getRefNo()));
            saveNotification(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user.getUserId(), assessorAllocation.getRteCode(), finalizedMessage,
                    URL, isHighPriority ? "#FADADD" : "#FFFFFF");
        } else if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION_ONLINE) {
            String messages = isHighPriority ? "You have been assigned High Prioritized Online Assessment the Job No:" : "You have been assigned Online Assessment the Job No:";
            String finalizedMessage = messages.concat(assessorAllocation.getJobId());

            String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(assessorAllocation.getRefNo()));
            saveNotification(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user.getUserId(), assessorAllocation.getRteCode(), finalizedMessage,
                    URL, isHighPriority ? "#FADADD" : "#FFFFFF");
        }
    }

    private void sendOnsiteAssessorNotAssignedSmsToAgent(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.CALL_ESTIMATE) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getPolicyDto().getCustName());
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getVehicleNo());
            smsParameterList.add(getNearestCity(connection, assessorAllocationDto.getClaimsDto().getNearestCity()));
            sendSmsMessage(connection, 25, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_AGENT);
        }
    }

    private void sendOnsiteAssessorAssignedSmsToAgent(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.ON_SITE_INSPECTION || assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.OFF_SITE_INSPECTION) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getPolicyDto().getCustName());
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getVehicleNo());
            smsParameterList.add(getNearestCity(connection, assessorAllocationDto.getClaimsDto().getNearestCity()));
            sendSmsMessage(connection, 24, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_AGENT);
        }
    }

    private void sendOnsiteAssessorNotAssignedSmsToCustomer(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.CALL_ESTIMATE) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getVehicleNo());
            smsParameterList.add(String.valueOf(assessorAllocationDto.getClaimsDto().getClaimNo()));
            sendSmsMessage(connection, 22, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getCustMobileNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        }
    }

    private void sendOnsiteAssessorAssignedSmsToCustomer(AssessorAllocationDto assessorAllocationDto, AssessorAllocationDto assessorAllocation, Connection connection, UserDto user) throws Exception {
        if (assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.ON_SITE_INSPECTION
                || assessorAllocation.getInspectionDto().getInspectionId() == AppConstant.OFF_SITE_INSPECTION) {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(assessorAllocationDto.getClaimsDto().getVehicleNo());
            smsParameterList.add(String.valueOf(assessorAllocationDto.getClaimsDto().getClaimNo()));
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getName());
            smsParameterList.add(assessorAllocationDto.getAssessorDto().getAssessorContactNo());
            sendSmsMessage(connection, 21, smsParameterList, assessorAllocationDto.getClaimsDto().getPolicyDto().getCustMobileNo(), assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType(), user, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        }
    }

    @Override
    public AssessorAllocationDto update(AssessorAllocationDto assessorAllocationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto delete(AssessorAllocationDto assessorAllocationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto updateAuthPending(AssessorAllocationDto assessorAllocationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto deleteAuthPending(AssessorAllocationDto assessorAllocationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto search(Object id) throws Exception {
        Connection connection = null;
        AssessorAllocationDto assessorAllocationDto = null;
        try {
            connection = getJDBCConnection();
            assessorAllocationDto = this.search(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return assessorAllocationDto;
    }

    public AssessorAllocationDto search(Connection connection, Object id) throws Exception {

        AssessorAllocationDto assessorAllocationDto = null;
        try {
            assessorAllocationDto = assessorAllocationDao.searchMaster(connection, id);
            if (null != assessorAllocationDto) {
                if (null != assessorAllocationDto.getInspectionDto()) {
                    InspectionDto inspectionDto = inspectionDao.searchMaster(connection, assessorAllocationDto.getInspectionDto().getInspectionId());
                    assessorAllocationDto.getInspectionDto().setInspectionValue(inspectionDto.getInspectionValue());
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return assessorAllocationDto;
    }

    @Override
    public InspectionDetailsDto getLatestUpdateOnSite(InspectionDetailsDto inspectionDetailsDto) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            InspectionDetailsDto latestInspection = inspectionDetailsDao.getLatestInspection(connection, inspectionDetailsDto.getClaimNo());
            MotorEngineerDetailsDto latestEngineer = motorEngineerDetailsDao.getLatestInspectionMotorEngineer(connection, inspectionDetailsDto.getClaimNo());

            if ((latestInspection == null) && (latestEngineer == null)) {
                return null;
            }
            if (null != latestEngineer) {
                OnSiteInspectionDetailsDto onsite = onSiteInspectionDetailsDao.searchMaster(connection, latestEngineer.getRefNo());
                inspectionDetailsDto.setChassisNo(latestInspection.getChassisNo());
                GarageInspectionDetailsDto garageInspectionDetailsDto = inspectionDetailsDto.getGarageInspectionDetailsDto();
                if (null != garageInspectionDetailsDto) {
                    inspectionDetailsDto.setChassisNo(latestEngineer.getChassisNo());
                    inspectionDetailsDto.setPav(latestEngineer.getPav());
                    inspectionDetailsDto.setDamageDetails(latestEngineer.getDamageDetails());
                    inspectionDetailsDto.setPad(latestEngineer.getPad());
                    inspectionDetailsDto.setGenuineOfAccident(latestEngineer.getGenuineOfAccident());
                    inspectionDetailsDto.setInvestReq(latestEngineer.getInvestReq());
                    inspectionDetailsDto.setAssessorSpecialRemark(latestInspection.getAssessorSpecialRemark());
                    inspectionDetailsDto.setAssessorSpecialRemark(latestEngineer.getAssessorSpecialRemark());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setBoldPercent(onsite.getBoldPercent());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setBoldTyrePenaltyAmount(onsite.getBoldTirePenaltyAmount());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setUnderPenaltyPercent(onsite.getUnderPenaltyPercent());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setUnderInsurancePenaltyAmount(onsite.getUnderPenaltyAmount());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setPayableAmount(onsite.getPayableAmount());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setAcr(onsite.getAcr());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setExcess(onsite.getExcess());
                    inspectionDetailsDto.setEngNoConfirm(latestEngineer.getEngNoConfirm());
                    inspectionDetailsDto.setMakeConfirm(latestEngineer.getMakeConfirm());
                    inspectionDetailsDto.setModelConfirm(latestEngineer.getModelConfirm());
                    inspectionDetailsDto.setYearMakeConfirm(latestEngineer.getYearMakeConfirm());
                    inspectionDetailsDto.setChassisNo(latestEngineer.getChassisNo());
                    inspectionDetailsDto.setChassisNoConfirm(latestEngineer.getChassisNoConfirm());
                    inspectionDetailsDto.setNotCheckedReason(latestEngineer.getNotCheckedReason());
//                    inspectionDetailsDto.setMileage(latestEngineer.getMileage());

//                    inspectionDetailsDto.setDesktopInspection(AppConstant.YES);

                    List<TireCondtionDto> tireCondtionDtos = tireCondtionMeDao.searchByClaimNoAndRefNo(connection, inspectionDetailsDto.getClaimNo(), latestEngineer.getRefNo());
                    inspectionDetailsDto.setTireCondtionDtoList(tireCondtionDtos);

                }
            } else if (null != latestInspection) {
                latestInspection = inspectionDetailsService.search(connection, latestInspection.getRefNo());
                inspectionDetailsDto.setChassisNo(latestInspection.getChassisNo());
                GarageInspectionDetailsDto garageInspectionDetailsDto = inspectionDetailsDto.getGarageInspectionDetailsDto();
                if (null != garageInspectionDetailsDto) {
                    inspectionDetailsDto.setChassisNo(latestInspection.getChassisNo());
                    inspectionDetailsDto.setPav(latestInspection.getPav());
                    inspectionDetailsDto.setDamageDetails(latestInspection.getDamageDetails());
                    inspectionDetailsDto.setPad(latestInspection.getPad());
                    inspectionDetailsDto.setGenuineOfAccident(latestInspection.getGenuineOfAccident());
                    inspectionDetailsDto.setInvestReq(latestInspection.getInvestReq());
                    inspectionDetailsDto.setEngNoConfirm(latestInspection.getEngNoConfirm());
                    inspectionDetailsDto.setMakeConfirm(latestInspection.getMakeConfirm());
                    inspectionDetailsDto.setAssessorSpecialRemark(latestInspection.getAssessorSpecialRemark());
//                    inspectionDetailsDto.setAssessorSpecialRemark(AppConstant.STRING_EMPTY);
                    inspectionDetailsDto.setModelConfirm(latestInspection.getModelConfirm());
                    inspectionDetailsDto.setYearMakeConfirm(latestInspection.getYearMakeConfirm());
                    inspectionDetailsDto.setChassisNo(latestInspection.getChassisNo());
                    inspectionDetailsDto.setChassisNoConfirm(latestInspection.getChassisNoConfirm());
                    inspectionDetailsDto.setNotCheckedReason(latestInspection.getNotCheckedReason());
//                    inspectionDetailsDto.setMileage(latestInspection.getMileage());

                    inspectionDetailsDto.getGarageInspectionDetailsDto().setBoldPercent(latestInspection.getOnSiteInspectionDetailsDto().getBoldPercent());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setBoldTyrePenaltyAmount(latestInspection.getOnSiteInspectionDetailsDto().getBoldTirePenaltyAmount());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setUnderPenaltyPercent(latestInspection.getOnSiteInspectionDetailsDto().getUnderPenaltyPercent());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setUnderInsurancePenaltyAmount(latestInspection.getOnSiteInspectionDetailsDto().getUnderPenaltyAmount());
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setPayableAmount(latestInspection.getOnSiteInspectionDetailsDto().getPayableAmount());
                    // inspectionDetailsDto.setDesktopInspection(AppConstant.YES);

                    List<TireCondtionDto> tireCondtionDtos = tireCondtionMeDao.searchByClaimNoAndRefNoAssessor(connection, inspectionDetailsDto.getClaimNo(), latestInspection.getRefNo());
                    inspectionDetailsDto.setTireCondtionDtoList(tireCondtionDtos);

                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return inspectionDetailsDto;

    }

    @Override
    public void rejectAssesorJob(AssessorAllocationDto assessorAllocationDto, UserDto user, String assignUser) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            int reasonId = 0;
            boolean isDesktop = false;
            reasonId = assessorAllocationDto.getRejectReasonNewDto().getReasonId();
            assessorAllocationDao.rejectJobStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), reasonId, assessorAllocationDto.getPreviousRefId());
            assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), assessorAllocationDto.getPreviousRefId());
            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, assessorAllocationDto.getClaimsDto().getClaimNo())) {
                if ((assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION ||
                        assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION)
                        && claimHandlerDao.isAutoStored(connection, assessorAllocationDto.getClaimsDto().getClaimNo())
                        && !motorEngineerDetailsDao.isAriSalvagePending(connection, assessorAllocationDto.getClaimsDto().getClaimNo())) {
                    claimHandlerDao.updateStoreStatus(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), "N");
                    saveClaimsLogs(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user, "Auto Re-Store File", "Claim File Auto Re-Stored due to Pending ARI/ Salvage Inspection Rejection");
                    saveClaimProcessFlow(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), 0, "Auto Re-Store File", "SYSTEM", Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                }
                sendNotificationForAssignUser(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user, assessorAllocationDto.getInspectionDto().getInspectionId());
            }
            if ((AppConstant.ARI_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId()
                    || AppConstant.SALVAGE_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId())) {
                RequestAriDto requestAriDto = new RequestAriDto();
                requestAriDto.setStatus("P");
                requestAriDto.setRefId(assessorAllocationDto.getPreviousRefId());
                requestAriDao.updateStatusAndRefIdByRefId(connection, requestAriDto);
            }
            if (AppConstant.DESKTOP_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId()) {
                isDesktop = true;
                inspectionDetailsDao.updateRecordStatusByRefNo(connection, ClaimStatus.REJECTED.getClaimStatus(), assessorAllocationDto.getPreviousRefId());
            }
            if (!assignUser.isEmpty()) {
                String URL = "/InspectionDetailsController/viewEdit?P_N_CLIM_NO=" + assessorAllocationDto.getClaimsDto().getClaimNo() + "&P_N_REF_NO=" + assessorAllocationDto.getPreviousRefId();
                saveNotification(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user.getUserId(), assignUser, "Inspection Job No: " + assessorAllocationDto.getPreviousJobId() + " has been Rejected", URL);
            }
            if (isDesktop) {
                saveClaimsLogs(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user, "RTE Job Rejected", "RTE Job " + assessorAllocationDto.getPreviousJobId() + " Rejected by " + user.getUserId());
            } else {
                saveClaimsLogs(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), user, "Assessor Job Rejected", "Assessor Job " + assessorAllocationDto.getPreviousJobId() + " Rejected by " + user.getUserId());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<UserDto> getReportingRteList() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return assessorAllocationDao.getReportingRteList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<AssessorDto> getAssessorListByRteCode(String rteCode) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return assessorDao.getAssessorListByRteCode(connection, rteCode);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public List<InspectionDto> getInspectionListForReassignScreen(int refId) throws Exception {
        Connection connection = null;
        List<InspectionDto> inspectionDtos = null;
        try {
            connection = getJDBCConnection();
            inspectionDtos = inspectionDao.searchInspectionForReassign(connection, refId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return inspectionDtos;
    }

    @Override
    public AssessorAllocationDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<AssessorAllocationDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<AssessorAllocationDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    private String getAssigningDateTime(AssessorAllocationDto assessorAllocationDto) {
        String assignDateTime = AppConstant.DEFAULT_DATE;
        try {
            if (!assessorAllocationDto.getAssigningDate().isEmpty()) {
                if (AppConstant.PM.equals(assessorAllocationDto.getAssignPeriod())) {
                    assignDateTime = Utility.getCombinedTimeString24hours(assessorAllocationDto.getAssignHours(), assessorAllocationDto.getAssignMinutes(), assessorAllocationDto.getAssignPeriod());

                } else {
                    assignDateTime = assessorAllocationDto.getAssignHours().concat(":").concat(assessorAllocationDto.getAssignMinutes());
                }
                if (!assessorAllocationDto.getAssigningDate().isEmpty() && !assignDateTime.isEmpty()) {
                    assignDateTime = assessorAllocationDto.getAssigningDate().concat(" ").concat(assignDateTime);
                }
            } else {
                assignDateTime = Utility.sysDateTime();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return assignDateTime;
    }

    private int getDuration(AssessorAllocationDto assessorAllocationDto) {
        int duration = 0;
        duration = (assessorAllocationDto.getDurationHours() * 60) + assessorAllocationDto.getDurationMinutes();
        return duration;
    }


    private void updateMaxJobId(Connection connection, AssessorAllocationDto assessorAllocationDto) {
        try {
            // Fixed Job Number Duplication Issue
            LOCK.lock();
            SequenceTableDto selectedKeyValue = sequenceKeyTableDao.getSelectedKeyValue(connection, AppConstant.JOB_TABLE_ID);

            //System Date to Reset Sequence
            String sysDate = Utility.sysDate("yyyyMMdd");

            //New Values
            int newKeyValue;
            String newSysDate;
            String formatNewSysDate;
            String jobNo;

            if (sysDate.equals(selectedKeyValue.getMaxSysDate())) {
                newKeyValue = selectedKeyValue.getMaxKeyValue();
                newSysDate = selectedKeyValue.getMaxSysDate();

            } else {
                newKeyValue = 1;
                newSysDate = sysDate;
                //jobNo = newSysDate + Utility.addZeroRJ(String.valueOf(newKeyValue), 4);
            }

            formatNewSysDate = Utility.getCustomDateFormat(newSysDate, AppConstant.DATE_FORMAT_INT, AppConstant.JOB_DATE_FORMAT);
            jobNo = formatNewSysDate + Utility.addZeroRJ(String.valueOf(newKeyValue), 4);

            if (null != selectedKeyValue) {
                assessorAllocationDto.setJobId(jobNo);
            }
            selectedKeyValue.setMaxKeyValue(newKeyValue + 1);
            selectedKeyValue.setMaxSysDate(newSysDate);
            selectedKeyValue.setTableId(AppConstant.JOB_TABLE_ID);
            sequenceKeyTableDao.updateMaster(connection, selectedKeyValue);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            LOCK.unlock();
        }
    }

    @Override
    public List<AssessorAllocationDto> getAssessorListByClaimNo(int claimId) throws Exception {
        List<AssessorAllocationDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = assessorAllocationDao.getAssessorListByClaimNo(connection, claimId);
            for (AssessorAllocationDto assessorAllocationDto : list) {
                if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    assessorAllocationDto.getInspectionDto().setInformedIfDesktop(desktopInspectionDetailsMeDao.isInformed(connection, assessorAllocationDto.getRefNo()));
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public boolean updateCompletedJob(int refId, UserDto user) throws Exception {
        Connection connection = null;
        boolean update = false;
        try {
            connection = getJDBCConnection();
            update = assessorAllocationDao.updateJobStatusByJobId(connection, ClaimStatus.COMPLETED.getClaimStatus(), Utility.sysDateTime(), user.getUserId(), Utility.sysDateTime(), refId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
        return update;
    }

    @Override
    public ErrorMessageDto saveRemark(SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception {
        Connection connection = null;
        ErrorMessageDto errorMessageDto = null;
        try {
            connection = getJDBCConnection();
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setDepartmentId(AppConstant.ASSESSOR_ALLOCATION_DEPARTMENT);
            specialRemarkDto.setSectionName(AppConstant.ASSESSOR_SECTION);
            specialRemarkDto = specialRemarkDao.insertMaster(connection, specialRemarkDto);

            if (null != specialRemarkDto) {
                errorMessageDto = new ErrorMessageDto();
                errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                errorMessageDto.setMessage("Remark Added");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return errorMessageDto;

    }

    @Override
    public AssessorAllocationDto reSendMessages(AssessorAllocationDto assessorAllocationDto) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();

            if (null != assessorAllocationDto.getAssessorDto()) {
                String assessorMobileNo = assessorDao.getAssessorMobileNo(connection, assessorAllocationDto.getAssessorDto().getCode().trim());
                String name = assessorDao.getAssessorName(connection, assessorAllocationDto.getAssessorDto().getCode());
                assessorAllocationDto.getAssessorDto().setName(name);
                assessorAllocationDto.getAssessorDto().setAssessorContactNo(assessorMobileNo);
                assessorAllocationDto.getAssessorDto().setCode(assessorAllocationDto.getAssessorDto().getCode().trim());
            }

            sendInspectionAssignedSmsToAssessor(connection, assessorAllocationDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return assessorAllocationDto;
    }

    @Override
    public List<UserDto> getRTEList() {
        Connection connection = null;
        List<UserDto> rteList = null;
        try {
            connection = getJDBCConnection();
            String accessUserTypes = "21,22,23,24";
            rteList = assessorAllocationDao.getRTEList(connection, accessUserTypes);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return rteList;
    }

    @Override
    public List<String> getInputUserList() {
        List<String> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = assessorAllocationDao.getInputUserList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<UserDto> getTCList() throws Exception {
        Connection connection = null;
        List<UserDto> rteList = null;
        try {
            connection = getJDBCConnection();
            String accessUserTypes = "25";
            rteList = assessorAllocationDao.getTCList(connection, accessUserTypes);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return rteList;
    }

    @Override
    public List<UserDto> getInitLiablityUserList() throws Exception {
        Connection connection = null;
        List<UserDto> rteList = null;
        try {
            connection = getJDBCConnection();
            //TODO
            String accessUserTypes = "25";
            rteList = assessorAllocationDao.getUserList(connection, accessUserTypes);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return rteList;
    }

    @Override
    public List<UserDto> getClaimHandlerUserList() throws Exception {
        Connection connection = null;
        List<UserDto> rteList = null;
        try {
            connection = getJDBCConnection();
            //TODO
            String accessUserTypes = "25";
            rteList = assessorAllocationDao.getUserList(connection, accessUserTypes);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return rteList;
    }

    @Override
    public List<NotificationDto> getNotifiList() throws Exception {
        Connection connection = null;
        List<NotificationDto> list = null;
        try {
            connection = getJDBCConnection();
            String curDateTime = Utility.sysDateTime();
            list = assessorAllocationDao.getNotifiList(connection, curDateTime);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public NotificationDto insertNotification(NotificationDto notificationDto) throws Exception {
        Connection connection = null;
        NotificationDto notification = null;
        try {
            connection = getJDBCConnection();
            notification = notificationDao.insertMaster(connection, notificationDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return notification;
    }

    @Override
    public ApplicationAlertDto insertAlert(ApplicationAlertDto applicationAlertDto) throws Exception {
        Connection connection = null;
        ApplicationAlertDto notification = null;
        try {
            connection = getJDBCConnection();
            notification = applicationAlertDao.insertMaster(applicationAlertDto, connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return notification;
    }

    @Override
    public ApplicationAlertDto searchAlert(Integer keyValue, Integer type) throws Exception {
        ApplicationAlertDto applicationAlert = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            applicationAlert = applicationAlertDao.searchByKeyValueAndType(connection, keyValue, type);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return applicationAlert;
    }

    @Override
    public List<InspectionDto> getInspectionList(Integer claimNo) throws Exception {
        Connection connection = null;
        List<InspectionDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<InspectionDto> inspectionDtos = inspectionDao.searchAll(connection);

            boolean onsiteType = assessorAllocationDao.getInspectionType(connection, claimNo, AppConstant.ON_SITE_INSPECTION);
            boolean offsiteType = assessorAllocationDao.getInspectionType(connection, claimNo, AppConstant.OFF_SITE_INSPECTION);
            boolean garageType = assessorAllocationDao.getInspectionType(connection, claimNo, AppConstant.GARAGE_INSPECTION);
            boolean drType = assessorAllocationDao.getInspectionType(connection, claimNo, AppConstant.DR_INSPECTION);
            boolean supplimantaryType = assessorAllocationDao.getInspectionType(connection, claimNo, AppConstant.SUP_INSPECTION);

            if (!onsiteType && !offsiteType && !garageType) {
                for (InspectionDto inspectionDto : inspectionDtos) {
                    if (inspectionDto.getInspectionId() != AppConstant.DESKTOP_INSPECTION
                            && inspectionDto.getInspectionId() != AppConstant.DR_INSPECTION && inspectionDto.getInspectionId() != AppConstant.SUP_INSPECTION) {
                        list.add(inspectionDto);
                    }
                }
            } else if (drType || supplimantaryType) {
                for (InspectionDto inspectionDto : inspectionDtos) {
                    if (inspectionDto.getInspectionId() != AppConstant.DESKTOP_INSPECTION && inspectionDto.getInspectionId() != AppConstant.GARAGE_INSPECTION) {
                        list.add(inspectionDto);
                    }
                }
            } else if (!onsiteType && !offsiteType) {
                for (InspectionDto inspectionDto : inspectionDtos) {
                    if (inspectionDto.getInspectionId() != AppConstant.DESKTOP_INSPECTION) {
                        list.add(inspectionDto);
                    }
                }
            } else {
                list = inspectionDtos;
            }


        } catch (Exception e) {

        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<InspectionDto> getOnsiteReviewInspectionList(Integer claimNo, boolean isFromOnsiteReview) throws Exception {
        Connection connection = null;
        List<InspectionDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<InspectionDto> inspectionDtos = inspectionDao.searchAll(connection);

            if (isFromOnsiteReview) {
                // When coming from Onsite Review with "Onsite Review" inspection type selected,
                // show only the onsite review specific inspection types
                for (InspectionDto inspectionDto : inspectionDtos) {
                    if (inspectionDto.getInspectionId() == AppConstant.ON_SITE_REVIEW_INSPECTION
                            || inspectionDto.getInspectionId() == AppConstant.OFF_SITE_REVIEW_INSPECTION) {
                        list.add(inspectionDto);
                    }
                }
            } else {
                // Default behavior - use existing logic
                return getInspectionList(claimNo);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public boolean checkValidInspectionType(Integer claimNo, Integer inspctionId) throws Exception {
        Connection connection = null;
        boolean isHave = false;
        try {
            connection = getJDBCConnection();
            isHave = assessorAllocationDao.getInspectionType(connection, claimNo, inspctionId);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return isHave;
    }

    private void sendInspectionAssignedSmsToAssessor(Connection connection, AssessorAllocationDto assessorAllocationDto) {
        try {
            //customer message
//            SmsParameterDto smsParameter = smsDao.getSmsParameter(connection, AppConstant.CUSTOMER_SMS);
//            SmsDto smsDto = new SmsDto();
//            smsDto.setDestination(assessorAllocationDto.getClaimsDto().getIntimationNo());
//            smsDto.setMessage(customerMessage(smsParameter.getSmsBody(), assessorAllocationDto));
//            smsDto.setMessagePriority(AppConstant.PIRORITY);
//            smsDto.setMessageType(AppConstant.MESSAGE_TYPE);
//            smsDto.setStatus(AppConstant.MESSAGE_STATUS);
//            smsDto.setInsertDateTime(Utility.sysDateTime());
//            smsDto.setSendDateTime(AppConstant.DEFAULT_DATE_TIME);
//            smsDto.setSmsStatus(AppConstant.SEND_STATUS_NEW);
//            smsDao.insertMaster(connection, smsDto);

            //assessor message
            SmsParameterDto smsParameter = smsDao.getSmsParameter(connection, AppConstant.ASSESSOR_SMS);
            switch (assessorAllocationDto.getInspectionDto().getInspectionId()) {
                case 1:
                    smsParameter = smsDao.getSmsParameter(connection, AppConstant.ONSITE_INSPECTION_SMS);
                    break;
                case 4:
                    smsParameter = smsDao.getSmsParameter(connection, AppConstant.GARAGE_INSPECTION);
                    break;
                case 5:
                    smsParameter = smsDao.getSmsParameter(connection, AppConstant.DR_INSPECTION_SMS);
                    break;
                case 6:
                    smsParameter = smsDao.getSmsParameter(connection, AppConstant.SUPPLEMENTARY_INSPECTION_SMS);
                    break;
                case 7:
                    smsParameter = smsDao.getSmsParameter(connection, AppConstant.ARI_INSPECTION);
                    break;
                case 9:
                    smsParameter = smsDao.getSmsParameter(connection, AppConstant.SALVAGE_INSPECTION_SMS);
                    break;
            }
            SmsDto smsDto = new SmsDto();
            smsDto.setDestination(replaceSpecialChar(assessorAllocationDto.getAssessorDto().getAssessorContactNo()));
            smsDto.setMessage(replaceSpecialChar(assessorMessage(connection, smsParameter.getSmsBody(), assessorAllocationDto)));
            smsDto.setMessagePriority(AppConstant.PIRORITY);
            smsDto.setMessageType(AppConstant.MESSAGE_TYPE);
            smsDto.setStatus(AppConstant.MESSAGE_STATUS);
            smsDto.setInsertDateTime(Utility.sysDateTime());
            smsDto.setSendDateTime(AppConstant.DEFAULT_DATE_TIME);
            smsDto.setSmsStatus(AppConstant.SEND_STATUS_NEW);
            smsDto.setPolicyChannelType(assessorAllocationDto.getClaimsDto().getPolicyDto().getPolicyChannelType());
            smsDao.insertMaster(connection, smsDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private String customerMessage(String message, AssessorAllocationDto assessorAllocationDto) {
        message = message.replace(AppConstant.PARAMETER_1, Integer.toString(assessorAllocationDto.getClaimsDto().getClaimNo()));
        message = message.replace(AppConstant.PARAMETER_2, assessorAllocationDto.getAssessorDto().getName());
        message = message.replace(AppConstant.PARAMETER_3, assessorAllocationDto.getAssessorDto().getAssessorContactNo());

        return message;
    }

    private String assessorMessage(Connection connection, String message, AssessorAllocationDto assessorAllocationDto) {

        message = message.replace(AppConstant.PARAMETER_1, assessorAllocationDto.getClaimsDto().getVehicleNo());
        message = message.replace(AppConstant.PARAMETER_2, assessorAllocationDto.getClaimsDto().getClaimNo().toString());
        message = message.replace(AppConstant.PARAMETER_3, assessorAllocationDto.getJobId());
        try {
            if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.ON_SITE_INSPECTION) {
                message = message.replace(AppConstant.PARAMETER_4, assessorAllocationDto.getPlaceOfinspection());
                message = message.replace(AppConstant.PARAMETER_5, assessorAllocationDto.getClaimsDto().getDateOfReport());
                message = message.replace(AppConstant.PARAMETER_6, assessorAllocationDto.getClaimsDto().getCliNo());
                message = message.replace(AppConstant.PARAMETER_7, assessorAllocationDto.getClaimsDto().getPolicyDto().getExcess().toString());
                message = message.replace(AppConstant.PARAMETER_8, assessorAllocationDto.getClaimsDto().getPolicyDto().getManufactYear().toString());
            } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
                message = message.replace(AppConstant.PARAMETER_4, getAssessorDto(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.ON_SITE_INSPECTION));
                message = message.replace(AppConstant.PARAMETER_5, assessorAllocationDto.getClaimsDto().getDateOfReport());
                message = message.replace(AppConstant.PARAMETER_6, assessorAllocationDto.getClaimsDto().getCliNo());
                message = message.replace(AppConstant.PARAMETER_7, assessorAllocationDto.getPlaceOfinspection());
                message = message.replace(AppConstant.PARAMETER_8, assessorAllocationDto.getClaimsDto().getPolicyDto().getManufactYear().toString());
            } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
                message = message.replace(AppConstant.PARAMETER_4, getAssessorDto(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.ON_SITE_INSPECTION));
                message = message.replace(AppConstant.PARAMETER_5, assessorAllocationDto.getClaimsDto().getDateOfReport());
                message = message.replace(AppConstant.PARAMETER_6, assessorAllocationDto.getClaimsDto().getCliNo());
                message = message.replace(AppConstant.PARAMETER_7, assessorAllocationDto.getPlaceOfinspection());
                message = message.replace(AppConstant.PARAMETER_8, assessorAllocationDto.getClaimsDto().getPolicyDto().getManufactYear().toString());
            } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
                message = message.replace(AppConstant.PARAMETER_4, getAssessorDto(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.ON_SITE_INSPECTION));
                message = message.replace(AppConstant.PARAMETER_5, getAssessorDto(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), AppConstant.GARAGE_INSPECTION));
                message = message.replace(AppConstant.PARAMETER_6, assessorAllocationDto.getClaimsDto().getDateOfReport());
                message = message.replace(AppConstant.PARAMETER_7, assessorAllocationDto.getClaimsDto().getCliNo());
                message = message.replace(AppConstant.PARAMETER_8, assessorAllocationDto.getPlaceOfinspection());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        message = message.concat(",Link:").concat(getAppUrl()).concat("?url=InspectionDetailsController/viewEdit%3FP_N_REF_NO=").
                concat(Integer.toString(assessorAllocationDto.getRefNo())).concat("%26P_N_CLIM_NO=").concat(Integer.toString(assessorAllocationDto.getClaimsDto().getClaimNo()));
        return message;
    }

    private String getAssessorDto(Connection connection, Integer claimNo, Integer inspectionId) {
        String assessorName = AppConstant.STRING_EMPTY;
        try {
            AssessorAllocationDto assessor = assessorAllocationDao.getAssessorAllocationByClaimNoAndInspectionId(connection, claimNo, inspectionId);
            AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, assessor.getRefNo());
            if (null != assessorAllocationDto) {
                assessorName = assessorAllocationDto.getAssessorDto().getName();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return assessorName;

    }

    private AssessorAllocationDto saveInspectionDetails(Connection connection, AssessorAllocationDto assessorAllocationDto, UserDto user) throws Exception {
        try {
            assessorAllocationDto.setRteDatetTime(Utility.sysDateTime());
            InspectionDetailsDto inspectionDetailsDto = new InspectionDetailsDto();
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            inspectionDetailsDto.setInspectionDto(assessorAllocationDto.getInspectionDto());
            inspectionDetailsDto.setInputDatetime(Utility.sysDateTime());
            inspectionDetailsDto.setInputUserId(user.getUserId());
            inspectionDetailsDto = inspectionDetailsDao.insertMaster(connection, inspectionDetailsDto);
            if (null != inspectionDetailsDto) {
                return assessorAllocationDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        }
        return null;
    }


    @Override
    public boolean updateCompletedJob(int refId, UserDto user, Connection connection) throws Exception {
        boolean update = false;
        try {
            update = assessorAllocationDao.updateJobStatusByJobId(connection, ClaimStatus.COMPLETED.getClaimStatus(), Utility.sysDateTime(), user.getUserId(), Utility.sysDateTime(), refId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("Can not Saved");
        }
        return update;
    }
}
