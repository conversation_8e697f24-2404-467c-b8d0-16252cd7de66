package com.misyn.mcms.utility;

public class AppConstant {
    public final static String ENV_UAT = "uat";
    public final static String ENV_UAT2 = "uat2";
    public final static String ACTION = "ACTION";
    public final static String UPDATE = "UPDATE";
    public final static String SAVE = "SAVE";
    public static final String DELETE = "D";
    public final static String AUTH_INSPECTION_DETAILS = "AUTH_INSPECTION_DETAILS";
    public final static String AUTH_ASSESSOR_FEE = "AUTH_ASSESSOR_FEE";
    public final static String SAVE_REPORT = "SAVE_REPORT";
    public final static String DATE_FORMAT = "yyyy-MM-dd";
    public final static String DATE_TIME_WITH_OUT_SECOND_FORMAT = "yyyy-MM-dd HH:mm";
    public final static String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public final static String DATE_TIME_FORMAT_INT = "yyyyMMddhhmmss";
    public final static String DATE_FORMAT_INT = "yyyyMMdd";
    public final static String DATE_ONLY_YEAR_FORMAT = "yyyy";
    public final static String DATE_ONLY_MONTH_FORMAT = "MM";
    public final static String JOB_DATE_FORMAT = "yyyy/MM/dd/";
    public final static String TIME_WITH_OUT_SECOND_FORMAT = "HH:mm";
    public final static String TIME_FORMAT = "HH:mm:ss";
    public final static String DEFAULT_DATE_TIME = "1980-01-01 00:00:00";
    public final static String DEFAULT_DATE = "1980-01-01";
    public final static String DEFAULT_TIME = "00:00:00";
    public final static String STRING_EMPTY = "";
    public final static String STRING_DOT = ".";
    public final static String STRING_UNDERSCORE_SIGN = "_";
    public final static String STRING_BACKSLASH_SIGN = "/";
    public final static String NOT_AVAILABLE = "N/A";
    public final static String ZERO = "0";
    public final static String INFORCE = "INF";
    public final static String PLEASE_SELECT_ONE_MESSAGE = "Please Select One";
    public final static int ZERO_VALUE = 0;
    public final static int ZERO_INT = 0;
    public final static int ONE_INT = 1;
    public final static String JAVASCRIPT_DATE_FORMAT = "MM/dd/yyyy";
    public static final String TODAY = "today";
    public static final String YES = "Y";
    public static final String NO = "N";
    public static final String AUTO_RESTORE = "AR";
    public static final String STRING_PENDING = "P";
    public static final String STRING_UPLOAD = "U";
    public static final String OTHER = "O";
    public static final String DIRECT_CUSTOMER = "Y";
    public static final String EXISTING_CUSTOMER = "N";
    public static final String CURRENT_DATE = "Current_Date";

    // Start Datatable request parameters
    public final static String START = "start";
    public final static String LENGTH = "length";
    public final static String COLUMN_INDEX = "order[0][column]";
    public final static String COLUMN_ORDER = "order[0][dir]";
    public final static String TAG_START_ORDER_COLUMN_NAME_DATA = "columns[";
    public final static String TAG_END_ORDER_COLUMN_NAME_DATA = "][data]";
    public final static String RECORD_TYPE_INPUT = "i";//Input
    public final static String RECORD_TYPE_AUTH = "a";//auth
    public final static String RECORD_TYPE_TEMP_PENDING = "p";//temp pending
    public final static String RECORD_TYPE_CANCEL_INVOICE = "cancel";//temp pending
    public final static String RECORD_TYPE_AUTH_CANCEL_INVOICE = "authcancel";//temp pending
    public final static String RECORD_TYPE_REGENERATE_INVOICE = "regenerate";//temp pending
    public final static String RECORD_TYPE_AUTH_REPORT = "authreport";//temp pending
    public final static String RECORD_TYPE_AUTH_REJECT_REPORT = "rejectreport";//temp pending
    public final static String RECORD_TYPE_AUTH_TEMPORARY_INVOICE = "temporary";//temp invoice pending
    public final static String RECORD_TYPE_DUPLICATE_INVOICE = "duplicate";//dublicate invoice print
    public final static String DRAFT = "DRAFT";
    public final static String ACTION_TYPE = "ACTION_TYPE";

    public final static String TXT_FROM_DATE = "txtFromDate";
    public final static String TXT_TO_DATE = "txtToDate";
    public final static String TXT_CLAIM_DATE = "txtClaimNumber";
    public final static String TXT_CLAIM_NUMBER = "txtClaimNumber";
    public final static String TXT_POL_NUMBER = "txtPolNumber";
    public final static String TXT_V_STATUS = "txtV_status";
    public final static String TXT_LOCATION = "txtLocation";
    public final static String TXT_REF_NUMBER = "txtRefNumber";
    public final static String TXT_VEHICLE_NUMBER = "txtVehicleNumber";
    public final static String TXT_INSURED_NAME = "txtInsuredName";
    public final static String TXT_ENGINE_NO = "txtEngineNo";
    public final static String TXT_INSURED_NIC = "txtInsuredNic";
    public final static String TXT_CHASSIS_NO = "txtChassisNo";
    public final static String TXT_POLICY_STATUS = "txtPolicyStatus";
    public final static String TXT_FOLLOWUP_CALL_DONE = "txtFollowupCallDone";
    public final static String TXT_CALL_USER_NAME = "txtCallUserName";
    public final static String POLICY_CHANNEL_TYPE = "policyChannelType";
    public final static String TXT_JOB_NO = "txtJobNumber";
    public final static String TXT_FILE_STATUS = "txtFileStatus";
    public final static String TXT_LIABILITY_STATUS = "txtLiabilityStatus";
    public final static String TXT_CAL_SHEET_STATUS = "txtCalSheetStatus";
    public final static String TXT_CALL_NO_OBJECTION = "txtCallNoObjection";
    public final static String TXT_CALL_NO_OBJECTION_LETTER_ATTACHED = "txtCallNoObjectionLetterAttached";
    public final static String TXT_PREMIUM_OUTSTANDING = "txtPremiumOutstanding";
    public final static String TXT_PREMIUM_OUTSTANDING_LETTER_ATTACHED = "txtPremiumOutstandingLetterAttached";
    public final static String TXT_FINALIZED_STATUS = "txtFinalizedStatus";
    public final static String TXT_CALSHEET_STATUS = "txtCalsheetStatus";
    public final static String TXT_ASSESEMENT_APPR_STATUS = "txtAssessmentApprStatus";
    public final static String TXT_ASSESEOR_APPR_STATUS = "txtAssessorApprStatus";
    public final static String TXT_INSPECTION_TYPE = "txtInspectionType";
    public final static String TXT_SUPPLIER_STAUS = "txtSupplierOrderStatus";
    public final static String TXT_OFFER_TYPE = "txtOfferType";
    public final static String NO_OF_DAYS = "noOfDays";
    public final static String TXT_PRIORITY = "txtPriority";
    public final static String TXT_CLI_NUMBER = "txtCliNumber";
    public final static String TXT_OTHER_CONTACT_NUMBER = "txtOtherContactNumber";
    public final static String TXT_POLICY_CHANNEL_TYPE = "cmbpolicyChannelType";
    public final static String TXT_ISF_CLAIM_NUMBER = "txtISFClaimNumber";
    public final static String TXT_REQUEST_USER = "txtV_requestUser";
    public final static String TXT_REQUEST_DATE_TIME = "txtRequestDate";
    public final static String TXT_REJECTION_PANEL_TYPE = "txtRejectionPanelType";
    public final static String TXT_REJECTION = "txtRejection";
    public final static String TXT_L_PANEL_STATUS = "txtLPanelStatus";
    public final static String TXT_D_MAKER = "txtDmaker";

    //Start Assign Assessor//
    public final static String ASSIGN_ASSESSOR = "assignAssessor";
    public final static String CITY_SERVICE = "cityService";
    public final static String DISTRICT_SERVICE = "districtService";
    public final static String DISTRICT_LIST = "districtList";
    public final static String ASSESSOR_ALLOCATION_SERVICE = "assessorAllocationService";
    public final static String CALL_CENTER_SERVICE = "callCenterService";
    public final static String DB_RECORD_COMMON_FUNCTION = "dbRecordCommonFunction";
    public final static String INSPECTION_DETAILS_SERVICE = "inspectionDetailsService";
    public final static String MOTOR_ENGINEER_SERVICE = "motorEngineerService";
    public final static String REQUEST_ARI_SERVICE = "requestAriService";
    public final static String CLAIM_HANDLER_SERVICE = "claimHandlerService";
    public final static String FINANCE_VOUCHER_DETAILS_SERVICE = "financeVoucherDetailsService";
    public final static String CALCULATION_SHEET_SERVICE = "calculationSheetService";
    public final static String REFERENCE_TWO_CALCULATION_SHEET_SERVICE = "referenceTwoCalculationSheetService";
    public final static String CLAIM_WISE_DOCUMENT_SERVICE = "claimWiseDocumentService";
    public final static String ACKNOWLEDGEMENT_SERVICE = "acknowledgementService";
    public final static String CLAIM_HANDLER_DASHBOARD_SERVICE = "claimHandlerDashboardService";
    public final static String DOCUMENT_HISTORY_SERVICE = "documentHistoryService";
    public final static String AM = "AM";
    public final static String PM = "PM";
    public final static int ACCEPTED = 24;
    public final static int PENDING = 27;
    public final static int JOB_TABLE_ID = 1;
    public final static int JOB_TABLE_ID_FOR_SUPPLY_ORDER = 2;
    public final static int CALL_ESTIMATE = 11;
    public final static int ASSESSOR_ALLOCATION_DEPARTMENT = 2;
    public final static int CLAIM_HANDLER_DEPARTMENT = 5;
    public final static String ASSESSOR_SECTION = "Assessor Allocation";
    public final static String SUCCESS_MESSAGE = "successMessage";
    public final static String SUCCESS_CODE = "successCode";
    public final static String ERROR_MESSAGE = "errorMessage";
    public final static int CUSTOMER_SMS = 1;
    public final static int ASSESSOR_SMS = 2;
    public final static int ONSITE_INSPECTION_SMS = 3;
    public final static int GARAGE_INSPECTION_SMS = 4;
    public final static int DR_INSPECTION_SMS = 5;
    public final static int SUPPLEMENTARY_INSPECTION_SMS = 6;
    public final static int ARI_INSPECTION_SMS = 7;
    public final static int SALVAGE_INSPECTION_SMS = 8;
    public final static int PIRORITY = 1;
    public final static String MESSAGE_TYPE = "cms";
    public final static int MESSAGE_STATUS = 1;
    public final static String SEND_STATUS_NEW = "N";
    public final static String PARAMETER_1 = "?1";
    public final static String PARAMETER_2 = "?2";
    public final static String PARAMETER_3 = "?3";
    public final static String PARAMETER_4 = "?4";
    public final static String PARAMETER_5 = "?5";
    public final static String PARAMETER_6 = "?6";
    public final static String PARAMETER_7 = "?7";
    public final static String PARAMETER_8 = "?8";
    public final static String PARAMETER_9 = "?9";
    public final static String PARAMETER_10 = "?10";
    public final static String TYPE = "TYPE";
    public final static String DOCUMENT_TYPE_ID = "documentTypeId";
    public final static String CLAIM_NO = "claimNo";
    public final static String JOB_REF_NO = "jobRefNo";
    public final static String PREV_ON_OR_OFF_SITE_REF_NO = "prevOnOrOffSiteRefNo";
    public final static String DEPARTMENT_ID = "departmentId";
    public final static String policyRefNo = "P_POL_REF_NO";
    public final static String claimsDto = "claimsDto";
    public final static int REJECTED = 23;
    public final static int ASSESSOR_MODULE = 3;
    public final static String ASSESSOR_MODULE_SECTION = "Assessor Module";
    public final static int MOTOR_ENGINEER_MODULE = 4;
    public final static String MOTOR_ENGINEER_MODULE_SECTION = "Motor Engineer Module";
    public final static String CLAIM_ID = "claimId";
    public final static String SEARCH_DOC_NAME = "SEARCH_DOC";
    public final static String PAYMENT_APPROVE_ERROR_MSG = "paymentApproveErrorMsg";
    public final static int REJECTION_LETTER_TYPE = 80;
    public final static String EXCESS_RESERVE_ERROR_MSG = "excessReserveErrorMsg";

    // End Datatable request parameters

    //session user
    public final static String SESSION_USER = "G_USER";
    public final static String SESSION_CLAIM_DTO = "claimsDto";
    public final static String SESSION_TYPE = "TYPE";
    public final static String SESSION_FIELD = "FIELD";
    public final static String SESSION_ASC_DESC_STATUS = "ASC_DESC_STATUS";
    public final static String SESSION_IS_ASC = "IS_ASC";
    public final static String SESSION_USER_RIGHT = "userRight";
    public final static String SESSION_G_MNUITMS = "G_MNUITMS";
    public final static String SESSION_CLICK_PAGE_NUMBER = "CLICK_PAGE_NUMBER";
    public final static String SESSION_SORT_FIELD = "P_SORT_FIELD";
    public final static String SESSION_P_ASC_DESC_STATUS = "P_ASC_DESC_STATUS";
    public final static String FORM_TYPE = "FORM_TYPE";
    public final static String CLAIM_DTO_TYPE_MASTER = "MASTER";
    public final static String CLAIM_DTO_TYPE_HISTORY = "HISTORY";
    public final static String CLAIM_DTO_HISTORY = "historyClaimsDto";
    public final static String ISMAP = "ISMAP";
    public final static String HIDE_ALLOCATION = "hideAllocation";
    public final static String MAPPED_POLICY_NO = "mappedPolicyNo";

    //hibernate and custom validation identify error codes
    public final static Integer NO_ERRORS_CODE = 200;
    public final static Integer WITH_ERRORS_CODE = 520;

    //claim save status
    public final static String STATUS_CLAIM_INTIMATE = "PS";
    public final static String STATUS_CLAIM_DRAFT = "DR";
    public final static String STATUS_CLAIM_REJECT = "REJ";
    public final static String STATUS_CLAIM_FOWORD = "FW";


    public static final String HTML = "html";
    public static final String JS = "js";
    public static final String CSS = "css";
    public static final String PNG = "png";
    public static final String JPG = "jpg";
    public static final String PDF = "pdf";
    public static final String ICO = "ico";
    public static final String JPEG = "jpeg";
    public static final String GIF = "gif";
    public static final String JFIF = "jfif";

    public static final String AUDIO = "audio";
    public static final String WOFF = ".woff";
    public static final String WOFF2 = ".woff2";
    public static final String TTF = ".ttf";
    public static final String SVG = "svg";
    public static final String TTF_CAP = ".TTF";
    public static final String EOT = ".eot";
    public static final String LOGIN_VALIDATE_SERVLET = "/LoginValidateServlet";

    public static final String G_DECRYPT_PASSWORD = "G_DECRYPT_PASSWORD";
    public static final String G_ITEMRIGHTS_OBJECT = "G_ITEMRIGHTS_OBJECT";
    public static final String G_MENUITEM_LIST = "G_MENUITEM_LIST";
    public static final String IS_DISABLED = "isDisabled";
    public final static String POL_NUMBER = "polNo";
    public final static Integer CALL_CENTER_DRAFT = 1;
    public final static Integer CALL_CENTER_DRAFTASSIGNED = 31;
    public final static String INSPECTION_DETAILS = "inspectionDetailsDto";
    public final static String MOTOR_ENGINEER_DETAILS = "motorEngineerDto";
    public final static String MOTOR_ENGINEER_DETAILS_PREVIOUS = "motorEngineerPreviousDto";
    public final static String SESSION_MOTOR_ENGINEER_DETAILS = "motorEngineerDto_SESSION";
    public final static String REQUESTED_ARI_LIST = "requestedAriList";
    public final static int ON_SITE_INSPECTION = 1;
    public final static int GARAGE_INSPECTION = 4;
    public final static int DESKTOP_INSPECTION = 8;
    public final static int DESKTOP_INSPECTION_ONLINE = 12;
    public final static int DR_INSPECTION = 5;
    public final static int SUP_INSPECTION = 6;
    public final static int ARI_INSPECTION = 7;
    public final static int SALVAGE_INSPECTION = 9;
    public final static int SUERVAY_INSPECTION = 15;
    public final static int LOCATION_INSPECTION = 16;
    public final static int ADDITIONAL_INSPECTION = 17;
    public final static int VERTUAL_GARAGE_INSPECTION = 18;
    public final static String REMARK_LIST = "remarkList";
    public final static String CONTACT_DETAIL_DTO = "contactDetailDto";
    public final static String PAYMENT_STATUS = "paymentStatus";
    public final static int SALVAGE_REQUEST = 2;
    public final static int ARI_REQUEST = 0;
    public final static int ARI = 1;
    public final static int ARI_SALVAGE = 3;
    public final static int COLLECT_SALVAGE = 4;


    public final static String SESSION_SFTP_DOCUMENT_SERVICE = "sftpDocumentService";
    public final static Integer ASSESSOR_DEPARTMENT_ID = 3;
    public final static Integer MOTOR_ENGINEER_DEPARTMENT_ID = 4;
    public final static String SESSION_CLAIM_DOCUMENT_DTO_LIST = "claimDocumentDtoList";
    public final static String SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST = "claimUploadViewDtoList";
    public final static String SESSION_CLAIM_IMAGE_DTO_LIST = "claimImageDtoList";
    public final static String SESSION_VIEWER_CLAIM_IMAGE_LIST = "viewerClaimImageList";
    public final static int ASSESSOR_MODULE_LOG = 4;
    public final static int MOTOR_ENGINEER_MODULE_LOG = 5;
    public final static int MOTOR_ENGINEER_ASSESSOR_PAYMENT_MODULE_LOG = 7;
    public final static int MOTOR_ENGINEER_INSPECTION_REPORT_MODULE_LOG = 8;
    public final static String ASSESSOR_AND_MOTOR_LOG = "4,5,7,8";
    public final static int CLAIM_HANDLER_MODULE_LOG = 6;
    public final static String PREVIOUS_CLAIM_LIST = "previousClaimList";
    public final static String PREVIOUS_INSPECTION = "PREVIOUS_INSPECTION";
    public final static String CHANGED = "changed";
    public final static String PREVIOUS_INSPECTION_DTO = "previousInspectionDto";
    public final static String PREVIOUS_INSPECTION_LIST = "previousInspectionList";
    public final static String IS_REQUESTED = "isRequested";
    public final static String HISTORY_CLAIM_UPLOADVIEW_DTO_LIST = "historyClaimUploadViewDtoList";
    public final static String HISTORY_CLAIM_DOCUMENT_DTO_LIST = "historyClaimDocumentDtoList";
    public final static String HISTORY_CLAIM_IMAGE_DTO_LIST = "historyClaimImageDtoList";
    public final static String HISTORY_VIEWER_CLAIM_IMAGE_LIST = "historyViewerClaimImageList";
    public final static String RTE_LIST = "rteList";
    public final static String APPROVE_STATUS = "approveStatus";
    public final static String APPROVE = "A";

    public final static int CLAIM_IMAGE_DOCUMENT_TYPE_ID = 12;
    public final static int DRIVING_LICENCE_DOCUMENT_TYPE_ID = 1;
    public final static int ESTIMATE_DOCUMENT_TYPE_ID = 3;
    public final static int GARAGE_ESTIMATE_DOCUMENT_TYPE_ID = 7;
    public final static int DR_ESTIMATE_DOCUMENT_TYPE_ID = 8;
    public final static int SUPPLEMENTARY_ESTIMATE_DOCUMENT_TYPE_ID = 9;
    public final static int DO_BILL_DOCUMENT_TYPE_ID = 128;
    public final static int BANK_DETAILS_DOCUMENT_TYPE_ID = 141;
    public final static int NO_OBJECTION_LETTER_DOCUMENT_TYPE_ID = 30;
    public final static int GARAGE_BILL_DOCUMENT_TYPE_ID = 129;
    public final static int PREMIUM_OUTSTANDING_CONFIRMATION_DOCUMENT_TYPE_ID = 65;
    //public final static String ALL_ESTIMATE_DOCUMENT_TYPE_IDS = "3,7,8,9";
    public final static String ALL_CLAIM_FORM_DOCUMENT_TYPE_IDS = "2,67";
    public final static String ALL_POLICE_REPORT_DOCUMENT_TYPE_IDS = "5,38,43,72";
    public static final Integer INVESTIGATION_REPORT_DOCUMENT_TYPE_ID = 62;
    public static final Integer TECHNICAL_CORDINATOR_ACCESSUSRTYPE = 25;
    public static final Integer TECHNICAL_CORDINATOR_NON_ARI_ACCESSUSRTYPE = 105;
    public static final Integer TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE = 104;

    public final static String PHOTO_COMPARISION_SERVICE = "photoComparisionService";
    public static final String PHOTO_COMPARISION_DTO = "photoComparisionDto";
    public static final String COMPARISION_TYPE = "comparisionType";
    public static final String INSPECTION_TYPE = "inspectionType";
    public static final String INSPECTION_JOB_NO = "inspectionJobNo";
    public static final String DOCUMENT_TYPE = "documentType";
    public static final String PHOTO = "Photo";
    public static final String DOCUMENT = "document";
    public static final String POLICY_REF_NO = "policyRefNo";
    public static final String POLICY_NO = "policyNumber";
    public static final String CLAIM_LOCK_SERVICE = "claimLockService";
    public static final String IS_LOCK = "isLock";
    public static final String SESSION_CLAIM_LOCK_DTO = "claimLockDto";
    public static final String NOTIFICATION_SERVICE = "notificationService";
    public static final String NOTIFICATION_FORM_DTO = "notificationFormDto";
    public static final String USER_RIGHTS_MANAGER_BEAN = "UserRightsManagerBean";
    public static final String USER_RIGHT_LIST = "userRightList";
    public static final String G_MENU_ITEM = "G_MNUITMS";
    public static final String P_MENU_ID = "P_MENUID";
    public static final String P_ITEM_ID = "P_ITEMID";
    public static final String RIGHT_I = "RIGHT_I";
    public static final String RIGHT_M = "RIGHT_M";
    public static final String RIGHT_D = "RIGHT_D";
    public static final String RIGHT_A1 = "RIGHT_A1";
    public static final String RIGHT_A2 = "RIGHT_A2";
    public static final String AUTHTYPE = "AUTHTYPE";
    public static final String SELECT_MENU_NAME = "select_menu_name";
    public static final String SELECT_SUB_MENU_NAME = "select_sub_menu_name";
    public static final String USER_RIGHT = "userRight";
    public static final String WELCOME_PAGE_URL = "welcomePageUrl";
    public static final String CLAIM_VIEW = "/CallCenter/viewClaim";
    public static final String MOTORENG_VIEW = "/MotorEngineerController/viewEdit";
    public static final String MOTORENG_CAL_VIEW = "/MotorEngineerController/viewReserveCheck";
    public static final String ASSESSOR_VIEW = "/InspectionDetailsController/viewEdit";
    public static final String CLAIM_HANDLER_VIEW = "/ClaimHandlerController/viewEdit";
    public static final String BILL_CHECK_VIEW = "/MotorEngineerController/viewBillCheck";
    public static final String SUPPLY_ORDER_CHECK_VIEW = "/MotorEngineerController/viewSupplyOrderCheck";
    public static final String SUPER_DASHBOARD_VIEW = "/DashboardController/viewSuperDashboard";
    public static final String ERROR = "P_ERROR";
    public static final String SESSION_CLAIM_NO = "sessionClaimNo";
    public static final String SESSION_COMPARISION_TYPE = "sessionComparisionType";
    public static final String SESSION_INSPECTION_TYPE = "sessionInspectionType";
    public static final String SESSION_INSPECTION_JOB_NO = "sessionInspectionJobNo";
    public static final String SESSION_DOCUMENT_TYPE = "sessionDocumentType";
    public static final String COMPARISION_TAB_NO = "comparisionTabNo";
    public static final int OFF_SITE_INSPECTION = 2;
    public final static int ON_SITE_REVIEW_INSPECTION = 13;
    public final static int OFF_SITE_REVIEW_INSPECTION = 14;
    public static final String CLAIM_HANDLER_DTO = "claimHandlerDto";
    public static final String LEASING_COMPANIES = "leasingCompanies";
    public static final String INTIMATION_REMARK = "Intimation Remark";
    public static final String LA_REMARK = "La Remark";
    public static final String OTHER_REMARK = "Other Remark";
    public static final String SPECIAL_APPROVAL_COMMENT = "Special Comment/Approval";
    public static final String BRANCH_REMARK = "Branch Remark";
    public static final String SPECIAL_COMMENT = "Special Comment";
    public static final String SPECIAL_REMARK = "Special Remark";
    public static final String CALSHEET_SPECIAL_REMARK = "Calsheet Special Remark";
    public static final String IR = "IR";
    public static final String LR = "LR";
    public static final String OR = "OR";
    public static final String SC = "SC";
    public static final String BR = "BR";
    public static final String SR = "SR";
    public static final String LOG_TARILS = "claimLogList";
    public static final String CLAIM_HANDLER_SPECIAL_REMARK = "claimHandlerSecialRemarkList";
    public static final String CLAIM_HANDLER_BRANCH_REMARK = "claimHandlerBranchRemarkList";
    public static final String P_N_CLIM_NO = "P_N_CLIM_NO";
    public static final String DOC_NAME = "DOC_NAME";
    public static final String CLAIM_WISE_DOCUMENT_DTO_LIST = "claimWiseDocumentDtoList";
    public static final String DOCUMENT_REQ_FROM_LIST = "documentReqFromList";
    public static final String SEARCH_DOC = "SEARCH_DOC";

    public static final int PARTIAL_LOSS = 1;
    public static final String CLAIM_REMINDER_PRINT_SERVICE = "reminderPrintService";
    public static final String REMINDER_PRINT_SUMMARY_DTO = "reminderPrintSummaryDto";
    public static final String REMINDER_LETTER_FORM_DTO = "reminderLetterFormDto";
    public static final String CLAIM_DOCUMENT_DTO = "claimDocumentDto";
    public static final String DOCUMENT_CHECK_STATUS = "A";
    public static final String DOCUMENT_HOLD_STATUS = "H";
    public static final String DOCUMENT_REJECT_STATUS = "R";
    public static final String IS_TWO_PANEL_USER = "IS_TWO_PANEL_USER";
    public static final String IS_FOUR_PANEL_USER = "IS_FOUR_PANEL_USER";
    public static final String IS_DECISION_MAKER = "IS_DECISION_MAKER";
    public static final String IS_INIT_LIABILITY_USER = "IS_INIT_LIABILITY_USER";
    public static final String IS_CLAIM_HANDLER_USER = "IS_CLAIM_HANDLER_USER";
    public static final String IS_OFFER_TEAM_INIT_LIABILITY_USER = "IS_OFFER_TEAM_INIT_LIABILITY_USER";
    public static final String IS_OFFER_TEAM_CLAIM_HANDLER_USER = "IS_OFFER_TEAM_CLAIM_HANDLER_USER";
    public static final String IS_SPARE_COORDINATOR = "IS_SPARE_COORDINATOR";
    public static final String IS_SCRUTINIZING_COORDINATOR = "IS_SCRUTINIZING_COORDINATOR";
    public static final String IS_SPECIAL_TEAM = "IS_SPECIAL_TEAM";
    public static final String IS_OFFER_TEAM_SPECIAL_TEAM = "IS_OFFER_TEAM_SPECIAL_TEAM";
    public static final String IS_MOFA_TEAM = "IS_MOFA_TEAM";
    public static final String IS_OFFER_TEAM_MOFA_TEAM = "IS_OFFER_TEAM_MOFA_TEAM";
    public static final String IS_ASSESSOR = "IS_ASSESSOR";
    public static final String IS_RTE = "IS_RTE";
    public static final String IS_LETTER_PANEL_USER = "IS_LETTER_PANEL_USER";
    public static final String IS_TECHNICAL_COORDINATOR_ARI_ONLY = "IS_TECHNICAL_COORDINATOR_ARI_ONLY";
    public static final String INVESTIGATION_DETAILS_DTO = "investigationDetailsDto";
    public static final String INVESTIGATION_DETAILS_FORM_DTO = "investigationDetailsFormDto";
    public static final String INVESTIGATION_DETAILS_FORM_DTO1 = "investigationDetailsFormDto1";
    public static final String INVESTIGATION_DETAILS_SERVICE = "investigationDetailsService";
    public static final String INVESTIGATOR_LIST = "investigatorList";
    public static final String SESSION_CLAIM_HANDLER_DTO = "sessionClaimHandlerDto";
    public static final String CLAIMPANEL_USER_SERVICE = "claimPanelUserService";
    public static final String MOBILE_APPLICATION_REQUEST_SERVICE = "mobileApplicationRequestService";
    public static final String CLAIM_RESERVE_ADJUSTMENT_SERVICE = "ClaimReserveAdjustmentService";
    public static final String SPARE_PART_DATABASE_SERVICE = "sparePartDatabaseService";
    public static final String SUPPLIER_LIST = "supplierList";
    public static final String SPARE_PART_LIST = "sparePartList";
    public static final String CLAIM_PANEL_USER_LIST = "claimPanelUserList";
    public static final String CLAIM_PANEL_LIST = "claimPanelList";
    public static final String SUPPLY_ORDER_SERVICE = "supplyOrderService";
    public static final String SUPPLY_ORDER_SUMMARY_DTO = "supplyOrderSummaryDto";
    public static final String TXT_ASSIGN_DATE_TIME = "InspectionDetailsGridDto";
    public static final String TXT_JOB_STATUS = "InspectionDetailsGridDto";
    public static final String TXT_ASSESSOR = "txtAssessor";
    public static final String TXT_CC_ASSIGNED_BY = "txtAssignedBy";
    public static final String TXT_RTE = "txtRte";
    public static final String SUPPLY_SUMMARY_LIST = "supplyOrderList";
    public static final String SUPPLY_ORDER_LIST = "supplyOrderListPending";
    public static final String VAT_RATE_LIST = "vatRateList";

    public static final String DAY_TYPE_LIST = "dayTypeList";
    public static final String INSPECTION_TYPE_LIST = "inspectionTypeList";
    public static final String INSPECTION_TIME_LIST = "inspectionTimeList";

    public final static Integer ACCESS_LEVEL_ASSESSOR = 20;
    public static final Integer ACCESS_LEVEL_SPARE_PARTS_COORDINATOR = 27;
    public static final Integer ACCESS_LEVEL_CLAIM_HANDLER = 41;
    public static final Integer ACCESS_LEVEL_RTE = 22;
    public static final String ACCESS_LEVEL_RTE_ASS_RTE_OPE_RTE = "22,23,24";
    public static final Integer ACCESS_LEVEL_INITIAL_LIABILITY = 40;
    public static final Integer ACCESS_LEVEL_SCRUTINIZING_TEAM = 28;
    public static final Integer ACCESS_LEVEL_DECISION_MAKER = 45;
    public static final Integer ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER = 46;
    public static final Integer ACCESS_LEVEL_OFFER_TEAM_INITIAL_LIABILITY = 60;
    public static final Integer ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER = 61;
    public static final Integer ACCESS_LEVEL_SPECIAL_TEAM = 43;
    public static final Integer ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM = 63;
    public static final Integer ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM = 62;
    public static final Integer ACCESS_LEVEL_MOFA_TEAM = 42;
    public static final Integer ACCESS_LEVEL_MAIN_PANEL = 48;
    public static final Integer ACCESS_LEVEL_SUB_PANEL = 47;
    public static final String ACCESS_LEVEL_CALL_CENTER = "'10', '11', '12', '13', '14'";
    public static final Integer LIABILITY_PENDING = 49;
    public static final String LIABILITY_VALUE = "P";
    public static final String STRING_LEVEL_MOFA_OFFER_TEAM = "12";
    public static final String STRING_LEVEL_MOFA_TEAM = "6";
    public static final String STRING_LEVEL_ONE = "1";

    public static final String AUTHOR = "MI Synergy pvt Ltd";
    public static final String COMPANY = " MI Synergy pvt Ltd";
    public static final String SESSION_CLAIM_USER_TYPE_DTO = "sessionClaimUserTypeDto";
    public static final String CLAIM_USER_LEAVE_SERVICE = "claimUserLeaveService";
    public static final String CLAIM_LEAVE_LIST = "popupClaimLeaveList";
    public static final String CLAIM_USER_LEAVE_LIST = "popupClaimUserLeaveList";
    public static final String CLAIM_USER_DOCUMENT_SERVICE = "claimDocumentTypeService";

    public static final String TAB_INDEX = "tabIndex";
    public static final String P_TAB_INDEX = "P_TAB_INDEX";
    public static final Integer TAB_INDEX_DOCUMENT_UPLOAD = 1;
    public static final Integer TAB_INDEX_INVESTIGATION = 5;
    public static final Integer TAB_INDEX_SUPPLY_ORDER = 6;

    public static final String CLAIM_SUPER_DASHBOARD_SERVICE = "claimSuperDashboardService";
    public static final String CLAIM_SUPER_DASHBOARD_DTO = "claimSuperDashboardDto";
    public static final String CLAIM_DASHBOARD_DTO = "claimDashboardDto";
    public static final Integer CLAIM_HANDLER_NORAML_FUNCTION = 1;
    public static final Integer CLAIM_HANDLER_INVESTIGATION_FUNCTION = 2;

    public static final String ASSESSOR_PAYMENT = "A";
    public static final String INVESTIGATION_PAYMENT = "I";
    public static final String ASSESSOR_PAYMENT_LIST = "assessorPaymentList";
    public static final String ASSESSOR_PAYMENT_SERVICE = "assessorPaymentService";

    public static final String PAYMENT_TYPE = "paymentType";
    public static final String STATUS = "status";
    public static final String ASSESSOR_NAME = "assessorName";
    public static final String TOTAL_AMOUNT = "totalAmount";

    public static final String TXT_MANUFACTURE_YEAR = "manufactureYear";
    public static final String TXT_VEHICLE_MODEL = "vehicleModel";
    public static final String TXT_VEHICLE_MAKE = "vehicleMake";
    public static final String TXT_SUPPLIER_NAME = "supplerName";
    public static final String TXT_SPAREPART_NAME = "sparePart";
    public static final String CLAIM_USER_UPDATE_SERVICE = "claimUserUpdateService";
    public static final String ALL_DEPARTMENT_ID_WITHOUT_CLAIM_HANDLER = "1,2,3,4";
    public static final Integer BRANCH_DEPARTMENT_ID = 6;
    public static final String RECORDE_STATUS = "A,R";
    public static final String SPARE_PART_ITEM_SERVISE = "sparePartItemService";
    public static final String MOTOR_ENGINEER_DETAILS_LIST = "motorEngineerDetailsList";
    public static final String CLAIM_DOCUMENT_SERVICE = "claimDocumentService";
    public static final String CLAIM_IMAGE_FORM_DTO_LIST = "claimImageFormDtoList";
    public static final String LOG_DETAILS = "engineerDetailsDto";
    public static final String CALCULATION_SHEET_NO = "calculationSheetNo";
    public static final String PENDING_ARI = "pendingAri";

    public static final String REOPEN_TYPE_EX_GRATIA = "EX";
    public static final String REOPEN_TYPE_NORMAL = "NORMAL";

    public static final String CLOSE_STATUS_PENDING = "PENDING";
    public static final String CLOSE_STATUS_CLOSE = "CLOSE";
    public static final String CLOSE_STATUS_REOPEN = "REOPEN";
    public static final String CLOSE_STATUS_SETTLE = "SETTLE";
    public static final String CLOSE_STATUS_SETTLE_PENDING = "SETTLE_PENDING";
    public static final String CLAIM_CALCULATION_SHEET_MAIN_DTO = "claimCalculationSheetMainDto";
    public static final Integer CALL_CENTER_LOG = 1;
    public static final String IS_TOTAL_LOSS_CLAIM_HANDLER_USER = "IS_TOTAL_LOSS_CLAIM_HANDLER_USER";
    public static final String IS_TECHNICAL_COORDINATOR = "IS_TECHNICAL_COORDINATOR";
    public static final String IS_BRANCH = "IS_BRANCH";
    public static final Integer TOTAL_LOSS_TYPE = 2;
    public static final Integer PARTIAL_LOSS_TYPE = 1;
    public static final Integer GARAGE_OFFER_TYPE = 5;
    public static final Integer ONSITE_OFFER_TYPE = 6;
    public static final Integer DESKTOP_OFFER_TYPE = 7;
    public static final Integer THEFT_TYPE = 7;
    public static final String DOC_TYPE_ESTIMATE = "'ESTIMATE'";
    public static final Integer THEFT = 4;
    public static final String SUPPLIER_DETAILS_SERVICE = "supplierDetailsService";
    public static final Integer ACCESS_LEVEL_INITIAL_LIABILITY_PENDING = 35;
    public static final String QUESTIONS_SYMBOLE = "?";
    public static final String PERCENTAGE_SYMBOLE = "%";
    public static final String EMAIL_REPLACE_SYMBOLE = "?";
    public static final String CONST_STATUS_JMS_PENDING = "JP";
    public static final String CONST_STATUS_JMS_SUCCESS = "JS";
    public static final Integer PREMIUM_CONFIRMATION_EMAIL = 1;
    public static final Integer CLAIM_DOCUMENT_REQUEST_REMINDER = 11;
    public static final Integer NO_OBJECTION_EMAIL = 2;
    public static final Integer VOUCER_GENERATE_EMAIL = 3;
    public static final Integer ASSESSOR_PAYEMNT_EMAIL = 4;
    public static final Integer PAYMENT_APPROVE_EMAIL = 5;
    public static final Integer NCB_EMAIL = 6;
    public static final Integer ARI_PENDING_MAIL = 7;
    public static final Integer MOFA_USER_EMAIL = 8;
    public static final Integer LARGE_CLAIM_MAIL = 9;
    public static final Integer INTIMATION_MAIL = 10;
    public static final Integer ASSESSOR_PAYMENT_APPROVE_MST_SEQUENCE_TABLE_ID = 3;
    public static final Integer CLAIM_DOCUMENT_REJECTION = 14;

    // offline table
    public static final String PAYEE_TYPE = "AS";
    public static final String VOUCHER_FLAG_ASSESSOR = "PRF";
    public static final String VOUCHER_FLAG = "PRV";
    public static final String INSURED = "PH";
    public static final String GARAGE = "WS";
    public static final String LEASING_COMPANY = "OT";
    public static final String NCB_REVERSAL = "OT";
    public static final String INSURANCE_COMPANY = "OT";
    public static final String GOVERNMENT_AUTHORITIES = "OT";
    public static final String THIRD_PARTY_INDIVIDUAL = "OT";
    public static final String THIRD_PARTY_COORPORATE = "OT";
    public static final String PAYEE_OTHER = "OT";
    public static final String VOUCER_GENERATE_CODE = "CNIC";
    public static final String PANEL_CATEGORY = "AS";
    public static final String IDENTIFICATION_CODE = "CNIC";
    public static final String LOSS_TYPE = "ODPL";
    public static final String INSPECTION_LIST = "inspectionList";
    public static final String IS_GARAGEINSPECTION = "IS_GARAGEINSPECTION";
    public static final Integer CAL_SHEET_TYPE_FULL_FINAL = 1;
    public static final Integer CAL_SHEET_TYPE_ADVANCED = 2;
    public static final Integer CAL_SHEET_TYPE_DO = 3;
    public static final Integer CAL_SHEET_TYPE_BALANCE = 4;
    public static final Integer CAL_SHEET_TYPE_RELEASE_ORDER = 5;
    public static final Integer CAL_SHEET_TYPE_THIRD_PARTY = 6;
    public static final Integer CAL_SHEET_TYPE_BALANCE_RELEASE_ORDER = 7;
    public static final Integer CAL_SHEET_TYPE_ON_SITE_OFFER = 9;
    public static final Integer CAL_SHEET_TYPE_ON_SITE_CLAIMEE_OFFER = 10;
    public static final Integer CAL_SHEET_TYPE_GARAGE_OFFER = 11;
    public static final Integer CAL_SHEET_PAYMENT_APPROVED = 65;
    public static final Integer CAL_SHEET_PAYMENT_REJECTED = 66;
    public static final Integer CAL_SHEET_VOUCHER_GENERATE = 67;
    public static final Integer CAL_SHEET_VOUCHER_GENERATE_PENDING = 70;
    public static final Integer CAL_SHEET_FORWARD_FOR_MOFA_APPROVAL = 64;
    public static final Integer CLAIM_REPUDIATED = 47;
    public static final String AGENT_GARAGE_SERVICE = "agentGarageService";
    public final static String PREFIX_POLICY = "COVNT";
    public final static Integer CLAIM_STELLE = 5;
    public final static Integer FULL_AND_FILE = 1;
    public static final String URL = "url";
    public static final String RETURN_URL = "RETURN_URL";
    public static final String EMPTY_STRING = "";
    public static final String TXT_ASSIGN__USER = "assignUser";
    public static final String TXT_USER_ID = "userId";
    public static final String TXT_LEAVE_TYPE = "leaveType";
    public static final String ZERO_DECIMAL = "0.00";
    public static final String CLAIM_DOCUMENT_TYPE_SERVICE = "claimDocumentTypeService";
    public static final Integer REVOKED = 69;
    public static final String COVER_NOTE = "COVNT";
    public static final String FILTERED_BY = "filterdBy";
    public static final String DASH = "-";
    public static final String TXT_LOSS_TYPE = "txtLossType";
    public static final String NOTIFICATION_READ_STATUS = "notificationReadStatus";
    public static final String NOTIFICATION_STATUS = "notificationStatus";

    public static final String ASSIGN_USER_NAME = "assignUserName";
    public static final String ASSIGN_USER_TYPE = "assignUserType";
    public static final String ASSIGN_USER_LEVEL = "assignUserLevel";

    public static final String ACCESS_LEVEL_CLAIM_HANDLER_AND_TOTAL_LOSS = "46,41";

    public final static String SESSION_CSRF_PREVENTION_SALT = "session_csrfPreventionSalt";
    public final static String CSRF_TOKEN = "csrfToken";
    public final static String CSRF_PREVENTION_SALT_CACHE = "csrfPreventionSaltCache";
    public final static String LOGIN_DO = "login.do";
    public final static String VOUCHER_LIST = "voucherList";
    public final static String VOUCHER_STATUS = "voucherStatus";
    public final static String VEHICLE_NUMBER = "vehicleNumber";
    public final static String VOUCHER_NUMBER = "voucherNumber";

    public final static String SEARCH_POLICY_NO = "searchPolicyNo";
    public final static String SEARCH_CHASSIS_NO = "searchChassisNo";
    public final static String SEARCH_ENGINE_NO = "searchEngineNo";
    public final static String SEARCH_REF_NUMBER = "searchRefNumber";
    public final static String SEARCH_VEHICLE_NUMBER = "searchVehicleNumber";
    public final static String SEARCH_INSURED_NAME = "searchInsuredName";
    public final static String SEARCH_INSURED_NIC = "searchInsuredNic";
    public final static String SEARCH_CLAIM_NUMBER = "searchClaimNumber";
    public final static String SEARCH_FROM_DATE = "searchFromDate";
    public final static String SEARCH_TO_DATE = "searchToDate";
    public final static String SEARCH_REJECTION_PANEL_TYPE = "searchRejectionPanelType";
    public final static String SEARCH_REJECTION = "searchRejection";
    public final static String SEARCH_D_MAKER = "searchDmaker";
    public final static String SEARCH_L_PANEL_STATUS = "searchLPanelStatus";
    public final static String SEARCH_CLAIM_STATUS = "searchClaimStatus";
    public final static String SEARCH_FOLLOWUP_CALL_DONE = "searchFollowupCallDone";
    public final static String SEARCH_LOCATION = "searchLocation";
    public final static String SEARCH_USER_NAME = "searchUserName";
    public final static String SEARCH_CLI_NUMBER = "searchCliNo";
    public final static String SEARCH_OTHER_CONTACT_NUMBER = "searchOtherContactNo";
    public final static String SEARCH_POLICY_STATUS = "searchPolicyStatus";
    public final static String SEARCH_FILE_STATUS = "searchFileStatus";
    public final static String SEARCH_POLICY_CHANNEL_TYPE = "searchPolicyChannelType";
    public final static String SEARCH_ISF_CLAIM_NUMBER = "searchISFClaimNo";
    public final static String SEARCH_LIABILITY_STATUS = "searchLiabilityStatus";
    public final static String SEARCH_FINALIZED_STATUS = "searchFinalizedStatus";
    public final static String FINANCE_REASON_LIST = "financeReasonList";
    public final static String POL_DELETE = "DEL";
    public final static String ADD = "ADD";
    public final static Integer COUSE_OF_LOSS_TYPE_TOTAL_LOSS = 4;
    public final static String REMOVE = "REMOVE";
    public final static String LATEST_INTIMATE_DATE_STATUS = "latestIntimateDateStatus";
    public final static String LATEST_INTIMATE_CALL_USER = "latestIntimateCallUser";
    public final static String ALL_DECISION_MAKER_STATUS = "'38','41','42','44','45','47','48','55','56','68'";
    public final static String SELECT_CLAIM_NO_AND_ASSIGN_USERS = "selectClaimNoAndAssignUsers";
    public final static String SELECT_CLAIM_NO_AND_JOB_NO = "selectClaimNoAndJobNo";
    public final static String IS_PENDING_SUPPLY_ORDER = "isSupplyOrderPEnding";
    public final static String CLAIMS_DTO = "claimsDto";
    public final static String ASSIGN = "ASSIGN";
    public final static String REASSIGN = "REASSIGN";
    public final static int CLAIM_STATUS_REOPEN = 20;
    public final static int CLAIM_STATUS_CLOSE = 21;
    public final static String TEMP_DTO = "tempDto";
    public final static Integer FORWARD_TO_D_MAKER_STATUS = 38;
    public final static Integer FORWARD_TO_2_MEMBER_STATUS = 39;
    public final static Integer FORWARD_TO_4_MEMBER_STATUS = 40;
    public final static String CALSHEET_STATUS_REJECTED = "REJECTED";
    public final static String CALSHEET_STATUS_ADVANCE_PAID = "ADVANCE_PAID";
    public final static String ADVANCE_PAID = "ADVANCE PAID";
    public final static String ADVANCE_AMOUNT = "AdvanceAmount";
    public final static String APPROVE_ADVANCE_AMOUNT = "ApproveAdvanceAmount";
    public final static String ASSESSOR_PAYMENT_DEDUCTION = "assessorPaymentDeduction";
    public final static String SPARE_PARTS_NAME = "sparePartsName";
    public final static String RECORDS_STATUS = "recordsStatus";
    public final static String SEARCH_RTE_CODE = "searchRteCode";
    public final static String SEARCH_ASSESSOR_NAME = "searchAssessorName";
    public final static String RTE_CODE = "rteCode";
    public final static String USER_NAME = "userName";
    public final static String ASSESSOR_LIST = "assessorList";
    public final static int SUBMITTED = 8;
    public final static int ASSIGNED = 29;

    public final static String ALREADY_SEND_CHANGE_REQUEST = "alreadySendChangeRequest";
    public final static String PAYMENT_APPROVE_OR_FORWARDED_TO_APPROVAL = "paymentApproveOrForwardedApproval";
    public final static int CLAIM_STATUS_INITIAL_LIABILITY_PENDING = 35;
    public final static int CLAIM_STATUS_INITIAL_LIABILITY_APPROVED = 36;
    public final static int CLAIM_STATUS_FORWARD_TO_DECISION_MAKER = 38;
    public final static int CLAIM_STATUS_FORWARD_TO_TWO_MEMBER_PANEL = 39;
    public final static int CLAIM_STATUS_FORWARD_TO_FOUR_MEMBER_PANEL = 40;
    public final static int CLAIM_STATUS_TWO_MEMBER_PANEL_REJECTION_APPROVED = 41;
    public final static int CLAIM_STATUS_TWO_MEMBER_PANEL_REJECTION_REJECTED = 42;
    public final static int CLAIM_STATUS_TWO_MEMBER_PANEL_REQUEST_INVESTIGATION = 43;
    public final static int CLAIM_STATUS_FOUR_MEMBER_PANEL_REJECTION_APPROVED = 44;
    public final static int CLAIM_STATUS_FOUR_MEMBER_PANEL_REJECTION_REJECTED = 45;
    public final static int CLAIM_STATUS_FOUR_MEMBER_PANEL_REQUEST_INVESTIGATION = 46;
    public final static int CLAIM_STATUS_CLAIM_REPUDIATED = 47;
    public final static int CLAIM_STATUS_LIABILITY_PENDING = 49;
    public final static int CLAIM_STATUS_LIABILITY_APPROVED = 50;
    public final static int CLAIM_STATUS_DECISION_MAKER_REQUEST_INVESTIGATION = 52;
    public final static int CLAIM_STATUS_DECISION_MAKER_INVESTIGATION_ARRANGED = 53;
    public final static int CLAIM_STATUS_INVESTIGATION_COMPLETED = 54;
    public final static int CLAIM_STATUS_INVESTIGATION_CANCEL = 55;
    public final static int CLAIM_STATUS_CLAIM_HANDLER_REQUEST_INVESTIGATION = 56;
    public final static int CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT = 57;
    public final static int CLAIM_STATUS_INVESTIGATION_REQUEST_APPROVED = 68;
    public final static int CLAIM_STATUS_RETURN_TO_DECISION_MAKER = 84;
    public final static int CLAIM_STATUS_FORWARDED_TO_THE_SPARE_PARTS_COORDINATOR_FOR_THE_CREATION_OF_CALCULATION_SHEET = 59;
    public final static int CLAIM_STATUS_FORWARDED_TO_THE_BILL_CHECK_TEAM_FOR_THE_RECOMMENDATION_OF_THE_CALCULATION_SHEET = 61;
    public final static String IS_COVER_NOTE = "IS_COVER_NOTE";
    public final static String ASSESSOR_DTO_LIST = "assessorDtoList";
    public final static int MOFA_TEAM_DEPARTMENT_ID = 7;
    public final static String SUCCESS = "SUCCESS";
    public final static String FAIL = "FAIL";
    public final static String USER_NOT_FOUND = "USER_NOT_FOUND";
    public final static String CAL_SHEET_ID = "calSheetId";
    public final static String LEVEL_CODE = "levelCode";
    public final static String PAYABLE_AMOUNT = "payableAmount";
    public final static int LEVEL_ONE = 1;
    public final static int LEVEL_THREE = 3;
    public final static int LEVEL_SIX = 6;
    public final static int LEVEL_SEVEN = 7;
    public final static int LEVEL_NINE = 9;
    public final static String INHOUSE = "INHOUSE";
    public final static String HYBRID = "HYBRID";
    public final static String FREELANCE = "FREELANCE";
    public final static String PERMANENT = "PERMANENT";
    public final static String PAYMENT_HOLD = "HOLD";
    public final static String PAYMENT_APPROVE = "APPROVE";
    public final static String PAYMENT_REJECT = "REJECT";
    public final static String HOLD = "H";
    public final static String REJECT = "R";
    public final static String RTE_DETAILS = "RTE_DETAILS";
    public final static String ASSESSOR_DETAILS = "ASSESSOR_DETAILS";
    public final static String ADD_VAT_WITH_NBT = "WITHNBT";
    public final static String ADD_VAT_WITHOUT_NBT = "WITHOUTNBT";


    public static final String REQUEST_FORM_ID = "requestFormId";
    public static final String BANK_DETAILS_CARD_ID = "cardId";
    public static final String BANK_DETAILS_PAYEE_TYPE = "payeeType";
    public static final String BANK_DETAILS_PAYEE_NAME = "payeeName";
    public static final String BANK_DETAILS_INSTRUMENT_TYPE = "instrumentType";
    public static final String DRIVER_DETAIL_DTO = "driverDetailDto";
    public static final String PENDING_INSPECTION = "pendingInspection";
    public static final String IS_UPLOADED = "isUploaded";
    public static final Integer AUTORITY_LIMIT_RTE = 4;


    public static final String PRIORITY_HIGH = "HIGH";
    public static final String PRIORITY_NORMAL = "NORMAL";

    public static final String CHANNEL_LIST = "channelList";
    public static final String CHANNEL_LIST_NOT_IN_TEAMS = "newChannelList";
    public static final String CHANNEL_USERS = "channelUserList";
    public static final String CHANNEL_TEAM = "channelTeam";
    public static final String CHANNEL_TEAM_LIST = "teamList";

    public static final String PRODUCT_LIST = "productList";
    public static final String PRODUCT_LIST_BY_ID = "productListById";
    public static final String BENEFIT_COVER_LOADING_LIST = "benefitCoverLoadingDetailList";
    public static final String CWE_LIST = "cweDetailList";
    public static final String CHARGES_DISCOUNT_LIST = "chargesAndDiscountList";
    public static final String SRCC_TC_LIST = "getSrccTcDetailList";

    public static final String BRANCH_DETAIL_DTO_LIST = "branchDetailDtoList";
    public static final String BRANCH_DETAIL_DTO = "branchDetailDto";
    public static final String BRANCH_CODE = "branchCode";
    public static final String BRANCH_NAME = "branchName";
    public static final String BRANCH_CITY = "branchCity";

    public static final String ACTIVE_STATUS = "A";

    public static final String IS_MOTOR_ENGINEERING = "IS_MOTOR_ENGINEERING";
    public static final int PROVIDE_OFFER = 1;

    public static final String CANCELLED_POLICY = "CAN";
    public static final String CALL_GO_COVER = "Call and Go";

    public static final String IS_PREVIOUS_CLAIM = "IS_PREVIOUS_CLAIM";

    public static final Integer RTE_LEVEL_1 = 1;
    public static final Integer RTE_LEVEL_2 = 2;
    public static final Integer RTE_LEVEL_3 = 3;

    public static final String URL_TYPE_TAKAFUL = "taka";
    public static final String URL_TYPE_CONVENTIONAL = "conv";
    public static final String DOC_UPLOAD = "docUpload";

    public static final String IS_HAVING_VOUCHER_GENERATED_CALSHEET = "hasVouGenCalSheet";
    public static final String IS_UPDATE = "isUpdate";
    public static final String MAX_DO = "MAX_DO";
    public static final String IS_NOTIFY = "isNotify";
    public static final String DO_REF_NO = "doRefNo";
    public static final String IS_DO_CREATION_REQUEST = "doRequest";
    public static final String IS_DO_ASSIGNED = "assignedDo";
    public static final Integer OFFER_SMS_TO_AGENT = 42;
    public static final Integer OFFER_SMS_TO_CUSTOMER = 43;
    public static final int LETTER_PANEL = 103;

    public static final Integer FORWARD_TO_SPARE_PARTS_COORD_FOR_ADVANCE_APPROVAL = 81;
    public static final Integer FORWARD_TO_BILL_CHECKING_TEAM_FOR_ADVANCE_APPROVAL = 82;
    public static final Integer FORWARD_TO_ENGINEER = 83;

    public static final String RTE_AUTH_LEVELS = "1,2,3,4";


    public static final String BIZ_TYPE_DIRECT = "D";
    public static final String BIZ_TYPE_CO_INSURANCE_FOLLOWER = "F";
    public static final String BIZ_TYPE_CO_INSURANCE_LEADER = "L";
    public static final String IS_READ_ONLY = "isReadOnly";
    public static final String INACTIVE_STATUS = "I";

    public static final String PANEL_DECISION = "panelDecisionDto";
    public static final String INPUT_USER = "inputUser";
    public static final String REJECTED_USER = "rejectedUser";
    public static final String IS_APPROVAL_PENDING = "isApprovalPending";
    public static final String IS_PANEL_DECISION = "isPanelDecision";
    public static final int MAIN_PANEL = 2;
    public static final String RETURN_BY_PANEL = "D";
    public static final String IS_THEFT = "isTheftClaim";
    public static final String REQUEST_ARI_DETAILS = "requestAriDto";
    public static final String PARTIALLY_REVIEW = "PR";
    public static final String FULLY_REVIEW = "FR";
    public static final String ARI_REQUESTED = "ariRequested";
    public static final String ARI_ARRANGED = "ariArranged";
    public static final String SALVAGE_ARRANGED = "salvageArranged";
    public static final String SALVAGE_REQUESTED = "salvageRequested";
    public static final Integer REVOKED_STATUS = 1;
    public static final Integer REJECTION_LETTER_TYPE_NO = 80;
    public static final Integer CLAIM_PENDING_DOCUMENTS_EMAIL = 12;


    public static final Integer DOCUMENT_TYPE_FINAL_BILL = 60;
    public static final Integer DOCUMENT_TYPE_SPARE_PARTS_BILL = 73;
    public static final Integer DOCUMENT_TYPE_lABOUR_BILL = 74;
    public static final Integer DOCUMENT_TYPE_TAX_INVOICE = 75;
    public static final String DOC_UPLOAD_DATE_TIME = "docUploadDateTime";
    public static final String BULK_CLOSE_DETAIL_DTO = "bulkCloseDto";
    public static final int INSURED_PAYEE_TYPE = 1;

    public static final String DEFAULT_NOTIFICATION_COLOR_CODE = "#FFFFFF";


    public static final String IS_ONLINE_INSPECTION = "isOnlineInspection";
    public static final String STRING_PRODUCT_NAME = "name";
    public static final String STRING_ID = "id";

    public static final String STRING_CODE = "code";

    public static final String SEND_TO_CUSTOMER = "Send Sms To Customer";
    public static final String SEND_TO_AGENT = "Send Sms To Agent";
    public static final String SEND_TO_INTRODUCER = "Send Sms To Introducer";
    public static final String CLAIM_FLOW_DETAIL_LIST = "ClaimFlowDetailList";

    public static final String HOLIDAY_TYPE_DETAIL = "holidayTypeDetail";
    public static final String HOLIDAY_TYPE_DETAIL_LIST = "holidayTypeDetailList";
    public static final Integer REJECTION_AUDIO_TYPE = 173;

    public static final String CONTENT_TYPE = "Content-Type";
    public static final String TOKEN_EXTRACTION_REGEX = "\"access_token\"\\s*:\\s*\"([^\"]+)\"";
    public static final String AUTHORIZATION = "Authorization";
    public static final String BEARER = "Bearer ";
    public static final String HTTP_POST = "POST";
    public static final String CLIENT_ID = "client_id=";
    public static final String CLIENT_SECRET = "&client_secret=";
    public static final String GRANT_TYPE = "&grant_type=";
    public static final String USERNAME = "&username=";
    public static final String PASSWORD = "&password=";
    public static final String RESPONSE_CONTENT_TYPE = "text/plain";
    public static final String RESPONSE_CHARSET = "UTF-8";
    public static final int OFFER_TYPE_ONSITE_OFFER = 1;
    public static final String SETTLEMENT_METHOD_GARAGE_OFFER = "5";
    public static final int OFFER_TYPE_ONSITE_CLAIMEE = 5;
    public static final int OFFER_PAYMENT_TYPE = 11;
}
