-- MCMS Onsite Review Enhancements Database Script
-- This script adds the new inspection types and updates the database schema
-- for the three onsite review enhancements

-- =====================================================
-- Enhancement 1: Add New Inspection Types for Onsite Review
-- =====================================================

-- Insert new inspection types for onsite review specific options
INSERT INTO claim_inspection_type (n_inspection_type_id, v_inspection_type_desc, v_status, d_created_date, v_created_user)
VALUES 
(13, 'On Site Review Inspection', 'A', NOW(), 'SYSTEM'),
(14, 'Off Site Review Inspection', 'A', NOW(), 'SYSTEM')
ON DUPLICATE KEY UPDATE 
v_inspection_type_desc = VALUES(v_inspection_type_desc),
d_last_updated_date = NOW(),
v_last_updated_user = 'SYSTEM';

-- =====================================================
-- Enhancement 2: Update Common Basket Table Schema
-- =====================================================

-- Add rejected_reason column if it doesn't exist
ALTER TABLE call_center_review_common_basket 
ADD COLUMN IF NOT EXISTS rejected_reason VARCHAR(500) NULL COMMENT 'Reason for job rejection';

-- Add reject_count column if it doesn't exist
ALTER TABLE call_center_review_common_basket 
ADD COLUMN IF NOT EXISTS reject_count INT DEFAULT 0 COMMENT 'Number of times job has been rejected';

-- Update status column to include 'R' for rejected status
ALTER TABLE call_center_review_common_basket 
MODIFY COLUMN status CHAR(1) DEFAULT 'P' COMMENT 'P=Pending, A=Assigned, C=Completed, R=Rejected';

-- =====================================================
-- Enhancement 3: Database Views for Inspection Type Descriptions
-- =====================================================

-- Create or update view for onsite review list with inspection type descriptions
CREATE OR REPLACE VIEW v_onsite_review_list AS
SELECT 
    ccb.*,
    cit.v_inspection_type_desc as inspection_type_description,
    CASE 
        WHEN ccb.status = 'P' THEN 'Pending'
        WHEN ccb.status = 'A' THEN 'Assigned'
        WHEN ccb.status = 'C' THEN 'Completed'
        WHEN ccb.status = 'R' THEN 'Rejected'
        ELSE 'Unknown'
    END as status_description
FROM call_center_review_common_basket ccb
LEFT JOIN claim_inspection_type cit ON ccb.inspection_type = cit.n_inspection_type_id
WHERE ccb.is_onsite_review_apply = 'Y';

-- =====================================================
-- Data Validation and Cleanup
-- =====================================================

-- Update any existing records with null reject_count to 0
UPDATE call_center_review_common_basket 
SET reject_count = 0 
WHERE reject_count IS NULL;

-- Verify the new inspection types were inserted correctly
SELECT n_inspection_type_id, v_inspection_type_desc, v_status 
FROM claim_inspection_type 
WHERE n_inspection_type_id IN (13, 14);

-- =====================================================
-- Index Creation for Performance
-- =====================================================

-- Create index on inspection_type for better join performance
CREATE INDEX IF NOT EXISTS idx_ccb_inspection_type 
ON call_center_review_common_basket(inspection_type);

-- Create index on status for filtering
CREATE INDEX IF NOT EXISTS idx_ccb_status 
ON call_center_review_common_basket(status);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_ccb_claim_inspection 
ON call_center_review_common_basket(claim_no, inspection_id);

-- =====================================================
-- Verification Queries
-- =====================================================

-- Check table structure
DESCRIBE call_center_review_common_basket;

-- Check inspection types
SELECT * FROM claim_inspection_type WHERE n_inspection_type_id >= 13;

-- Check view creation
SELECT COUNT(*) as total_onsite_review_records FROM v_onsite_review_list;

COMMIT;
