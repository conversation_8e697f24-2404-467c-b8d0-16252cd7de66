# MCMS Onsite Review Enhancements - Implementation Summary

## Overview
This document summarizes the three enhancements implemented for the MCMS Onsite Review functionality, focusing on backend logic and database operations for proper data persistence and status transitions.

## Enhancement 1: Dynamic Inspection Type Logic for Onsite Review to Assessor Allocation Flow

### Implementation Details
- **New Constants Added**: Added `ON_SITE_REVIEW_INSPECTION = 13` and `OFF_SITE_REVIEW_INSPECTION = 14` to `AppConstant.java`
- **Service Method**: Created `getOnsiteReviewInspectionList()` in `AssessorAllocationService` interface and implementation
- **Controller Updates**: Modified `AssessorAllocationController` to check for `onsiteReview` parameter and use the new service method
- **Logic**: When `isFromOnsiteReview` is true, only shows inspection types 13 and 14; otherwise uses existing logic

### Key Files Modified
- `src/main/java/com/misyn/mcms/utility/AppConstant.java`
- `src/main/java/com/misyn/mcms/claim/service/AssessorAllocationService.java`
- `src/main/java/com/misyn/mcms/claim/service/impl/AssessorAllocationServiceImpl.java`
- `src/main/java/com/misyn/mcms/claim/controller/callcenter/AssessorAllocationController.java`

### Database Changes
- New inspection types (13, 14) added to `claim_inspection_type` table

## Enhancement 2: Job Rejection Handling with Reason and Count Tracking

### Implementation Details
- **DAO Methods**: Added `rejectOnsiteReviewJob()` and `updateStatusToPendingJob()` to `CallCenterCommonBasketDao`
- **Service Method**: Created `rejectOnsiteReviewJobAndMoveToPending()` in `CommonBasketService` with transaction management
- **Status Enum**: Added `REJECTED("R")` status to `CallCenterCommonBasketDto.Status` enum
- **Database Fields**: Added `rejected_reason` and `reject_count` columns to track rejection details

### Key Features
- Captures rejection reason and stores it in the database
- Automatically increments rejection count using `COALESCE(reject_count, 0) + 1`
- Sets status to 'R' (Rejected) temporarily, then moves back to 'P' (Pending)
- Uses database transactions to ensure data consistency
- Proper error handling and rollback mechanisms

### Key Files Modified
- `src/main/java/com/misyn/mcms/claim/dao/motorengineer/CallCenterCommonBasketDao.java`
- `src/main/java/com/misyn/mcms/claim/dao/impl/motorengineer/CallCenterCommonBasketDaoImpl.java`
- `src/main/java/com/misyn/mcms/claim/service/CommonBasketService.java`
- `src/main/java/com/misyn/mcms/claim/service/impl/CommonBasketServiceImpl.java`
- `src/main/java/com/misyn/mcms/claim/dto/motorengineer/CallCenterCommonBasketDto.java`

### Database Schema Changes
- Added `rejected_reason VARCHAR(500)` column
- Added `reject_count INT DEFAULT 0` column
- Updated `status` column to support 'R' for rejected

## Enhancement 3: Display Inspection Type Description in Onsite Review List

### Implementation Details
- **Query Enhancement**: Modified `fetchOnsiteReviewList()` and `fetchAll()` methods to join with `claim_inspection_type` table
- **DTO Enhancement**: Added `inspectionTypeDescription` field to `CallCenterCommonBasketDto`
- **Database View**: Created `v_onsite_review_list` view for easier querying with descriptions

### Key Features
- Uses LEFT JOIN to get inspection type descriptions from `claim_inspection_type` table
- Returns user-friendly descriptions instead of numeric codes
- Maintains backward compatibility with existing code
- Optimized with proper indexing for performance

### Key Files Modified
- `src/main/java/com/misyn/mcms/claim/dao/impl/motorengineer/CallCenterCommonBasketDaoImpl.java`
- `src/main/java/com/misyn/mcms/claim/dto/motorengineer/CallCenterCommonBasketDto.java`

### SQL Query Changes
```sql
-- Before
SELECT * FROM call_center_review_common_basket WHERE 1=1

-- After  
SELECT ccb.*, cit.v_inspection_type_desc as inspection_type_description 
FROM call_center_review_common_basket ccb 
LEFT JOIN claim_inspection_type cit ON ccb.inspection_type = cit.n_inspection_type_id 
WHERE 1=1
```

## Database Migration Script
- **File**: `database/mcms_onsite_review_enhancements.sql`
- **Contents**: 
  - New inspection type insertions
  - Schema alterations for rejection handling
  - Index creation for performance
  - Database view creation
  - Verification queries

## Status Transition Flow

### Normal Flow
1. **Pending Job** (`status = 'P'`) → **Onsite Review List** (`status = 'A'`)
2. **Onsite Review List** → **Assessor Allocation** (with dynamic inspection types)
3. **Assessor Allocation** → **Assessment Details** (inspection type reflected)

### Rejection Flow
1. **Onsite Review List** → **Rejected** (`status = 'R'`, reason captured, count incremented)
2. **Rejected** → **Pending Job** (`status = 'P'`) - automatic transition

## Testing Recommendations
1. Test dynamic inspection type loading when coming from onsite review
2. Verify rejection reason capture and count increment
3. Test status transitions between Pending Job ↔ Onsite Review List
4. Validate inspection type descriptions display correctly
5. Test transaction rollback scenarios for rejection handling

## Performance Considerations
- Added database indexes on frequently queried columns
- Used LEFT JOIN to avoid data loss when inspection types are missing
- Implemented proper transaction management for data consistency
- Optimized queries to minimize database round trips

## Future Enhancements
- Add audit trail for rejection history
- Implement notification system for rejected jobs
- Add reporting capabilities for rejection analytics
- Consider adding rejection reason categories/dropdown
